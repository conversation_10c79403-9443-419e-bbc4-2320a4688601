import 'dart:io';

import 'package:diogeneschatbot/features/ai_planner/presentation/pages/ai_planner_page.dart';
import 'package:diogeneschatbot/features/ai_tutor/presentation/pages/ai_tutor_page.dart';
import 'package:diogeneschatbot/features/comics/presentation/pages/story_diffusion_page.dart';
import 'package:diogeneschatbot/features/podcast/presentation/pages/generate_audio_page.dart';
import 'package:diogeneschatbot/features/recipes/create_recipies_page.dart';
import 'package:diogeneschatbot/features/story/presentation/pages/story_page.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/bot_page.dart';
import 'package:diogeneschatbot/pages/chat_ai_page.dart';
import 'package:diogeneschatbot/pages/chat_gpt_researcher_page.dart';
import 'package:diogeneschatbot/pages/chat_rooms_page.dart';
import 'package:diogeneschatbot/pages/chat_writing_crewai_page.dart';
import 'package:diogeneschatbot/pages/document_page.dart';
import 'package:diogeneschatbot/pages/image_page.dart';
import 'package:diogeneschatbot/pages/read_pdf_page.dart';
import 'package:diogeneschatbot/pages/smart_search_page.dart';
import 'package:diogeneschatbot/pages/transcription_page.dart';
import 'package:diogeneschatbot/pages/translate_page.dart';
import 'package:diogeneschatbot/pages/user_posts_page.dart';
import 'package:diogeneschatbot/screens/chat_mindsearch_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:diogeneschatbot/utils/local_llm_factory.dart';
// import 'package:diogeneschatbot/pages/yolo_page.dart';

class UsageListWidget extends StatelessWidget {
  const UsageListWidget({
    super.key,
    required this.currentUserId,
    required this.items,
  });

  final String? currentUserId;
  final List<Usage> items;

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      child: GridView.builder(
        padding: EdgeInsets.all(1),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 1,
          mainAxisSpacing: 5,
          childAspectRatio: 2,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) {
          return FractionallySizedBox(
            widthFactor: 0.85, // Width relative to GridView width
            child: GestureDetector(
              key: Key("HomeScreenUsageChoiceButton_${items[index].type}"),
              onTap: () {
                // Navigate to the appropriate page based on the item's type
                switch (items[index].type) {
                  // case UsageType.news:
                  //   Navigator.push(
                  //     context,
                  //     MaterialPageRoute(
                  //         builder: (context) => RssFeedPage(
                  //               currentUserId: currentUserId!,
                  //             )),
                  //   );
                  //   break;
                  case UsageType.transcription:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TranscriptionPage(
                          title: "Transcription",
                          currentUserId: currentUserId!,
                        ),
                      ),
                    );
                    break;
                  case UsageType.translate:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TranslatePage(
                          title: "Translate",
                          currentUserId: currentUserId!,
                          usage: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.generateImage:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => GenerateImagePage(
                          title: "Chat",
                          type: items[index],
                          currentUserId: currentUserId!,
                        ),
                      ),
                    );
                    break;
                  case UsageType.post:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            UserPostsPage(userId: currentUserId!),
                      ),
                    );
                    break;
                  case UsageType.chatWithPeople:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            ChatRoomsPage(currentUserId: currentUserId!),
                      ),
                    );
                    break;
                  case UsageType.myBots:
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => BotPage()),
                    );
                    break;
                  case UsageType.createBot:
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => StoragePage()),
                    );
                    break;
                  case UsageType.readBook:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            ReadPDFPage(pdfData: null, fileName: null),
                      ),
                    );
                    break;
                  // case UsageType.yolo:
                  //   Navigator.push(
                  //     context,
                  //     MaterialPageRoute(builder: (context) => YOLOApp())
                  //   );
                  case UsageType.localLLM:
                    final localLLMPage = getLocalLLMPage();

                    localLLMPage.navigate(context);
                    break;
                  case UsageType.write:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ChatWritingCrewAIPage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.smartSearch:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SmartSearchPage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.personalizedSearch:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ChatMindSearchScreen(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.gptResearcher:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => GPTResearcherPage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.aiTripPlanner:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ItineraryPlannerPage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.generateComic:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => StoryDiffusionReplicatePage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.createRecipe:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => CreateRecipePage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.generateAudio:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => GenerateAudioPage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.storyTelling:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => StoryCreationPage(
                          title: items[index].onscreenMessage,
                          type: items[index],
                        ),
                      ),
                    );
                    break;
                  case UsageType.tutor:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AITutorPage(
                          title: items[index].onscreenMessage,
                          usage: items[index],
                        ),
                      ),
                    );
                    break;
                  default:
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            ChatPage(title: "Chat", type: items[index]),
                      ),
                    );
                    break;
                }
              },
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Use Expanded to make the image fill the available space
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.0),
                      // Optional: Add rounded corners
                      child: Image.asset(
                        items[index].imagePath,
                        // Add the path to your image here
                        fit: BoxFit.cover,
                        // Ensure the image covers the area
                        // width: double.infinity, // Make the image take full width
                        height:
                            double.infinity, // Make the image take full height
                      ),
                    ),
                  ),
                  // SizedBox(height: 8),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 1),
                    child: Text(
                      items[index].onscreenMessage,
                      textAlign: TextAlign.center,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
