import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer' as developer;
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/quiz.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/entities/learning_session.dart';
import '../../domain/repositories/ai_tutor_repository.dart';
import '../../domain/use_cases/generate_learning_plan_use_case.dart';
import '../../domain/use_cases/create_flashcards_use_case.dart';
import '../../domain/use_cases/track_progress_use_case.dart';
import '../../domain/use_cases/generate_quiz_use_case.dart';
import '../../domain/use_cases/explain_concept_use_case.dart';
import '../../domain/use_cases/identify_knowledge_gaps_use_case.dart';
import '../../domain/use_cases/load_study_recommendations_use_case.dart';

part 'ai_tutor_event.dart';
part 'ai_tutor_state.dart';

/// BLoC for managing AI Tutor functionality
class AITutorBloc extends Bloc<AITutorEvent, AITutorState> {
  final GenerateLearningPlanUseCase _generateLearningPlan;
  final CreateFlashcardsUseCase _createFlashcards;
  final TrackProgressUseCase _trackProgress;
  final GenerateQuizUseCase _generateQuiz;
  final ExplainConceptUseCase _explainConcept;
  final IdentifyKnowledgeGapsUseCase _identifyKnowledgeGaps;
  final LoadStudyRecommendationsUseCase _loadStudyRecommendations;

  AITutorBloc({
    required GenerateLearningPlanUseCase generateLearningPlan,
    required CreateFlashcardsUseCase createFlashcards,
    required TrackProgressUseCase trackProgress,
    required GenerateQuizUseCase generateQuiz,
    required ExplainConceptUseCase explainConcept,
    required IdentifyKnowledgeGapsUseCase identifyKnowledgeGaps,
    required LoadStudyRecommendationsUseCase loadStudyRecommendations,
  }) : _generateLearningPlan = generateLearningPlan,
       _createFlashcards = createFlashcards,
       _trackProgress = trackProgress,
       _generateQuiz = generateQuiz,
       _explainConcept = explainConcept,
       _identifyKnowledgeGaps = identifyKnowledgeGaps,
       _loadStudyRecommendations = loadStudyRecommendations,
       super(const AITutorInitial()) {
    on<GenerateLearningPlanEvent>(_onGenerateLearningPlan);
    on<CreateFlashcardsEvent>(_onCreateFlashcards);
    on<GenerateQuizEvent>(_onGenerateQuiz);
    on<StartLearningSessionEvent>(_onStartLearningSession);
    on<EndLearningSessionEvent>(_onEndLearningSession);
    on<LoadLearningProgressEvent>(_onLoadLearningProgress);
    on<UpdateLearningProgressEvent>(_onUpdateLearningProgress);
    on<LoadLearningPlansEvent>(_onLoadLearningPlans);
    on<SaveLearningPlanEvent>(_onSaveLearningPlan);
    on<ExplainConceptEvent>(_onExplainConcept);
    on<IdentifyKnowledgeGapsEvent>(_onIdentifyKnowledgeGaps);
    on<LoadStudyRecommendationsEvent>(_onLoadStudyRecommendations);
    on<ResetAITutorEvent>(_onResetAITutor);
  }

  Future<void> _onGenerateLearningPlan(
    GenerateLearningPlanEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(
      const AITutorLoading(
        message: 'Generating your personalized learning plan...',
      ),
    );

    final result = await _generateLearningPlan(
      GenerateLearningPlanParams(
        subject: event.subject,
        currentLevel: event.currentLevel,
        learningGoals: event.learningGoals,
        preferences: event.preferences,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (learningPlan) => emit(LearningPlanGenerated(learningPlan)),
    );
  }

  Future<void> _onCreateFlashcards(
    CreateFlashcardsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Creating flashcards...'));

    final result = await _createFlashcards(
      CreateFlashcardsParams(
        topic: event.topic,
        count: event.count,
        difficulty: event.difficulty,
        context: event.context,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (flashcards) => emit(FlashcardsCreated(flashcards)),
    );
  }

  Future<void> _onGenerateQuiz(
    GenerateQuizEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Generating adaptive quiz...'));

    final result = await _generateQuiz(
      GenerateQuizParams(
        topic: event.topic,
        concepts: event.concepts,
        difficulty: event.difficulty,
        previousResults: event.previousResults,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (quiz) => emit(QuizGenerated(quiz)),
    );
  }

  Future<void> _onStartLearningSession(
    StartLearningSessionEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Starting learning session...'));

    try {
      // Implement proper session start logic with persistence
      // Validate session parameters and user authentication
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to start a learning session',
          ),
        );
        return;
      }

      // Validate session parameters
      if (event.subject.isEmpty) {
        emit(
          const AITutorError('Subject is required to start a learning session'),
        );
        return;
      }

      if (event.topic.isEmpty) {
        emit(
          const AITutorError('Topic is required to start a learning session'),
        );
        return;
      }

      // Initialize with baseline assessment based on user's previous performance
      double initialScore = 0.0;
      try {
        // Get user's historical performance for this subject/topic
        final progressResult = await _trackProgress(
          TrackProgressParams(
            userId: currentUser.uid,
            subject: event.subject,
            topic: event.topic,
          ),
        );

        progressResult.fold(
          (failure) => initialScore = 0.0, // Default if no previous progress
          (progress) => initialScore = progress?.stats.averageQuizScore ?? 0.0,
        );
      } catch (e) {
        // If progress tracking fails, start with 0.0
        initialScore = 0.0;
      }

      final now = DateTime.now();
      final session = LearningSession(
        id: 'session_${now.millisecondsSinceEpoch}',
        userId: currentUser.uid,
        subject: event.subject,
        topic: event.topic,
        startTime: now,
        status: LearningSessionStatus.active,
        conceptsCovered: event.conceptsCovered,
        comprehensionScore: initialScore, // Initialize with baseline assessment
        metadata: {
          'difficulty': 'medium', // Default difficulty
          'learningStyle': 'visual', // Default learning style
          'sessionGoals': <String>[], // Empty goals list
          'estimatedDuration': 30, // Default 30 minutes
          'createdAt': now.toIso8601String(),
          'userPreferences': {
            'enableHints': true,
            'enableFeedback': true,
            'adaptiveDifficulty': true,
          },
          'conceptsCount': event.conceptsCovered.length,
          'sessionType': 'general_learning',
        }, // Add session metadata like difficulty level, preferences
      );

      // Save session to repository/database
      // Note: In a real implementation, you'd save this to a database
      // For now, we'll emit the session as started
      emit(LearningSessionStarted(session));
    } catch (e) {
      emit(AITutorError('Failed to start learning session: ${e.toString()}'));
    }
  }

  Future<void> _onEndLearningSession(
    EndLearningSessionEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Ending learning session...'));

    try {
      // Implement session end logic
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to end a learning session',
          ),
        );
        return;
      }

      // Validate session ID
      if (event.sessionId.isEmpty) {
        emit(
          const AITutorError(
            'Session ID is required to end a learning session',
          ),
        );
        return;
      }

      // Validate comprehension score
      if (event.comprehensionScore < 0.0 || event.comprehensionScore > 1.0) {
        emit(
          const AITutorError('Comprehension score must be between 0.0 and 1.0'),
        );
        return;
      }

      final now = DateTime.now();

      // In a real implementation, you would:
      // 1. Fetch the existing session from the database
      // 2. Update it with end time and final scores
      // 3. Save the updated session
      // 4. Update user's learning progress
      // 5. Generate session summary and recommendations

      // For now, create a completed session with the provided data
      final session = LearningSession(
        id: event.sessionId,
        userId: currentUser.uid,
        subject: event.metadata['subject'] as String? ?? 'General',
        topic: event.metadata['topic'] as String? ?? 'Learning',
        startTime: event.metadata['startTime'] != null
            ? DateTime.parse(event.metadata['startTime'] as String)
            : now.subtract(const Duration(minutes: 30)),
        endTime: now,
        status: LearningSessionStatus.completed,
        conceptsCovered:
            (event.metadata['conceptsCovered'] as List<dynamic>?)
                ?.map((e) => e.toString())
                .toList() ??
            [],
        comprehensionScore: event.comprehensionScore,
        metadata: {
          ...event.metadata,
          'endedAt': now.toIso8601String(),
          'sessionDuration': event.metadata['startTime'] != null
              ? now
                    .difference(
                      DateTime.parse(event.metadata['startTime'] as String),
                    )
                    .inMinutes
              : 30,
          'finalScore': event.comprehensionScore,
          'completionStatus': 'completed',
        },
      );

      // Save session to local storage and update user progress
      try {
        // In a full implementation, this would save to a database
        // For now, we'll emit the session as part of the state
        developer.log(
          'Learning session completed: ${session.id}',
          name: 'AITutorBloc',
        );
      } catch (e) {
        developer.log(
          'Failed to save learning session: $e',
          name: 'AITutorBloc',
          error: e,
        );
      }

      emit(LearningSessionEnded(session));
    } catch (e) {
      emit(AITutorError('Failed to end learning session: ${e.toString()}'));
    }
  }

  Future<void> _onLoadLearningProgress(
    LoadLearningProgressEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading learning progress...'));

    final result = await _trackProgress(
      TrackProgressParams(
        userId: event.userId,
        subject: event.subject,
        topic: event.topic,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (progress) => emit(LearningProgressLoaded(progress)),
    );
  }

  Future<void> _onUpdateLearningProgress(
    UpdateLearningProgressEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Updating progress...'));

    try {
      // Implement progress update logic
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError('User must be authenticated to update progress'),
        );
        return;
      }

      // Validate progress data
      if (event.progress.userId != currentUser.uid) {
        emit(
          const AITutorError('Progress update not authorized for this user'),
        );
        return;
      }

      if (event.progress.overallProgress < 0.0 ||
          event.progress.overallProgress > 1.0) {
        emit(
          const AITutorError('Overall progress must be between 0.0 and 1.0'),
        );
        return;
      }

      // In a real implementation, you would:
      // 1. Validate the progress data
      // 2. Save the updated progress to the database
      // 3. Update any related analytics or recommendations
      // 4. Trigger notifications if milestones are reached

      // Create updated progress with current timestamp
      final updatedProgress = event.progress.copyWith(
        lastUpdated: DateTime.now(),
        metadata: {
          ...event.progress.metadata,
          'lastUpdateSource': 'user_action',
          'updatedAt': DateTime.now().toIso8601String(),
        },
      );

      // Save progress to local storage
      try {
        // In a full implementation, this would save to a database
        developer.log(
          'Learning progress updated for user: ${updatedProgress.userId}',
          name: 'AITutorBloc',
        );
      } catch (e) {
        developer.log(
          'Failed to save learning progress: $e',
          name: 'AITutorBloc',
          error: e,
        );
      }

      emit(LearningProgressUpdated(updatedProgress));
    } catch (e) {
      emit(AITutorError('Failed to update learning progress: ${e.toString()}'));
    }
  }

  Future<void> _onLoadLearningPlans(
    LoadLearningPlansEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading learning plans...'));

    try {
      // Implement learning plans loading
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to load learning plans',
          ),
        );
        return;
      }

      // In a real implementation, you would:
      // 1. Fetch learning plans from the database for the current user
      // 2. Filter by subject if specified
      // 3. Sort by creation date or progress
      // 4. Apply any additional filters

      // For now, return an empty list with a note
      // In a full implementation, this would query a database
      developer.log(
        'Loading learning plans for user: ${event.userId}',
        name: 'AITutorBloc',
      );

      emit(
        const LearningPlansLoaded([]),
      ); // Empty list for now - replace with actual data
    } catch (e) {
      emit(AITutorError('Failed to load learning plans: ${e.toString()}'));
    }
  }

  Future<void> _onSaveLearningPlan(
    SaveLearningPlanEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Saving learning plan...'));

    try {
      // Implement learning plan saving
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        emit(
          const AITutorError(
            'User must be authenticated to save learning plans',
          ),
        );
        return;
      }

      // Validate learning plan data
      if (event.plan.userId != currentUser.uid) {
        emit(
          const AITutorError('Learning plan save not authorized for this user'),
        );
        return;
      }

      if (event.plan.title.isEmpty) {
        emit(const AITutorError('Learning plan title is required'));
        return;
      }

      if (event.plan.subject.isEmpty) {
        emit(const AITutorError('Learning plan subject is required'));
        return;
      }

      if (event.plan.milestones.isEmpty) {
        emit(
          const AITutorError('Learning plan must have at least one milestone'),
        );
        return;
      }

      // In a real implementation, you would:
      // 1. Validate the learning plan data
      // 2. Save the plan to the database
      // 3. Update user's learning plan list
      // 4. Generate initial recommendations

      // Create updated plan with current timestamp
      final updatedPlan = event.plan.copyWith(
        lastUpdated: DateTime.now(),
        preferences: {
          ...event.plan.preferences,
          'savedAt': DateTime.now().toIso8601String(),
          'version': '1.0',
        },
      );

      // Save learning plan to local storage
      try {
        // In a full implementation, this would save to a database
        developer.log(
          'Learning plan saved: ${updatedPlan.id}',
          name: 'AITutorBloc',
        );
      } catch (e) {
        developer.log(
          'Failed to save learning plan: $e',
          name: 'AITutorBloc',
          error: e,
        );
      }

      emit(LearningPlanSaved(updatedPlan));
    } catch (e) {
      emit(AITutorError('Failed to save learning plan: ${e.toString()}'));
    }
  }

  Future<void> _onExplainConcept(
    ExplainConceptEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Generating explanation...'));

    final result = await _explainConcept(
      ExplainConceptParams(
        concept: event.concept,
        context: event.context,
        style: event.style,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (explanation) => emit(
        ConceptExplained(
          concept: event.concept,
          explanation: explanation,
          style: event.style,
        ),
      ),
    );
  }

  Future<void> _onIdentifyKnowledgeGaps(
    IdentifyKnowledgeGapsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Analyzing knowledge gaps...'));

    final result = await _identifyKnowledgeGaps(
      IdentifyKnowledgeGapsParams(
        quizResults: event.quizResults,
        subject: event.subject,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (knowledgeGaps) => emit(
        KnowledgeGapsIdentified(
          knowledgeGaps: knowledgeGaps,
          subject: event.subject,
        ),
      ),
    );
  }

  Future<void> _onLoadStudyRecommendations(
    LoadStudyRecommendationsEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorLoading(message: 'Loading study recommendations...'));

    final result = await _loadStudyRecommendations(
      LoadStudyRecommendationsParams(
        userId: event.userId,
        subject: event.subject,
      ),
    );

    result.fold(
      (failure) => emit(AITutorError(failure.message)),
      (recommendations) => emit(StudyRecommendationsLoaded(recommendations)),
    );
  }

  Future<void> _onResetAITutor(
    ResetAITutorEvent event,
    Emitter<AITutorState> emit,
  ) async {
    emit(const AITutorInitial());
  }
}
