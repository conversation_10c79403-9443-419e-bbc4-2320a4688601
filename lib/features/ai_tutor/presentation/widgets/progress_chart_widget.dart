import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/entities/learning_progress.dart';
import '../../domain/repositories/ai_tutor_repository.dart';
import '../../data/repositories/mock_ai_tutor_repository.dart';
import '../../domain/services/ai_content_service.dart';

/// Enhanced progress chart widget with interactive charts and analytics
/// Displays learning progress, quiz scores, and study streaks
/// Integrates with real user progress data from the repository
class ProgressChartWidget extends StatefulWidget {
  final List<ProgressData>? progressData;
  final String chartType;
  final String title;
  final String? subject;
  final String? topic;

  const ProgressChartWidget({
    super.key,
    this.progressData,
    this.chartType = 'line',
    this.title = 'Learning Progress',
    this.subject,
    this.topic,
  });

  @override
  State<ProgressChartWidget> createState() => _ProgressChartWidgetState();
}

class _ProgressChartWidgetState extends State<ProgressChartWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'Week';
  String _selectedMetric = 'Score';
  late AITutorRepository _repository;
  List<ProgressData>? _cachedProgressData;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _repository = MockAITutorRepository(aiContentService: AIContentService());
    _loadProgressData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Loads real progress data from repository
  Future<void> _loadProgressData() async {
    if (widget.progressData != null) {
      _cachedProgressData = widget.progressData;
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // Load progress data from repository
        final progressResult = await _repository.getLearningProgress(
          userId: user.uid,
          subject: widget.subject ?? 'General',
          topic: widget.topic,
        );

        progressResult.fold(
          (failure) {
            // TODO: Implement proper error handling instead of fallback to mock data
            _cachedProgressData = _generateMockProgressData();
          },
          (learningProgress) {
            if (learningProgress != null) {
              _cachedProgressData = _convertLearningProgressToProgressData(
                learningProgress,
              );
            } else {
              // TODO: Show empty state or onboarding instead of mock data
              _cachedProgressData = _generateMockProgressData();
            }
          },
        );
      } else {
        // TODO: Handle unauthenticated state properly instead of showing mock data
        _cachedProgressData = _generateMockProgressData();
      }
    } catch (e) {
      // TODO: Implement proper error handling and user feedback instead of mock data
      _cachedProgressData = _generateMockProgressData();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        elevation: 4,
        margin: EdgeInsets.all(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    final progressData = _cachedProgressData ?? _generateMockProgressData();

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    // Period selector
                    DropdownButton<String>(
                      value: _selectedPeriod,
                      items: ['Day', 'Week', 'Month', 'Year']
                          .map(
                            (period) => DropdownMenuItem(
                              value: period,
                              child: Text(period),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedPeriod = value!;
                          _loadProgressData(); // Reload data when period changes
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    // Metric selector
                    DropdownButton<String>(
                      value: _selectedMetric,
                      items: ['Score', 'Time', 'Streak']
                          .map(
                            (metric) => DropdownMenuItem(
                              value: metric,
                              child: Text(metric),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedMetric = value!;
                        });
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Tab bar for different chart types
            TabBar(
              controller: _tabController,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(icon: Icon(Icons.show_chart), text: 'Line'),
                Tab(icon: Icon(Icons.bar_chart), text: 'Bar'),
                Tab(icon: Icon(Icons.pie_chart), text: 'Pie'),
              ],
            ),
            const SizedBox(height: 16),

            // Chart content
            SizedBox(
              height: 300,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLineChart(progressData),
                  _buildBarChart(progressData),
                  _buildPieChart(progressData),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Summary statistics
            _buildSummaryStats(progressData),
          ],
        ),
      ),
    );
  }

  Widget _buildLineChart(List<ProgressData> data) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Text(
                    '${data[index].date.day}/${data[index].date.month}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: data
                .asMap()
                .entries
                .map(
                  (entry) => FlSpot(
                    entry.key.toDouble(),
                    entry.value.score.toDouble(),
                  ),
                )
                .toList(),
            isCurved: true,
            color: Theme.of(context).primaryColor,
            barWidth: 3,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart(List<ProgressData> data) {
    return BarChart(
      BarChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Text(
                    '${data[index].date.day}/${data[index].date.month}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        barGroups: data
            .asMap()
            .entries
            .map(
              (entry) => BarChartGroupData(
                x: entry.key,
                barRods: [
                  BarChartRodData(
                    toY: entry.value.score.toDouble(),
                    color: Theme.of(context).primaryColor,
                    width: 16,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                ],
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildPieChart(List<ProgressData> data) {
    // Calculate subject-wise progress breakdown from actual data
    final subjectData = _calculateSubjectBreakdown(data);

    if (subjectData.isEmpty) {
      return const Center(
        child: Text(
          'No data available for pie chart',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: subjectData.entries
            .map(
              (entry) => PieChartSectionData(
                value: entry.value,
                title: '${entry.value.toInt()}%',
                color: _getColorForSubject(entry.key),
                radius: 100,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            )
            .toList(),
        centerSpaceRadius: 40,
        sectionsSpace: 2,
      ),
    );
  }

  /// Calculates subject-wise progress breakdown from progress data
  Map<String, double> _calculateSubjectBreakdown(List<ProgressData> data) {
    if (data.isEmpty) return {};

    // Group data by subject and calculate averages
    final subjectGroups = <String, List<ProgressData>>{};
    for (final item in data) {
      subjectGroups.putIfAbsent(item.subject, () => []).add(item);
    }

    final subjectAverages = <String, double>{};
    for (final entry in subjectGroups.entries) {
      final avgScore =
          entry.value.map((d) => d.score.toDouble()).reduce((a, b) => a + b) /
          entry.value.length;
      subjectAverages[entry.key] = avgScore;
    }

    // If only one subject, show time distribution instead
    if (subjectAverages.length == 1) {
      return _calculateTimeDistribution(data);
    }

    return subjectAverages;
  }

  /// Calculates time distribution when only one subject is available
  Map<String, double> _calculateTimeDistribution(List<ProgressData> data) {
    final totalMinutes = data
        .map((d) => d.timeSpent.inMinutes.toDouble())
        .reduce((a, b) => a + b);

    if (totalMinutes == 0) {
      return {'No Activity': 100.0};
    }

    // Group by day of week for time distribution
    final dayGroups = <String, double>{};
    for (final item in data) {
      final dayName = _getDayName(item.date.weekday);
      dayGroups[dayName] = (dayGroups[dayName] ?? 0) + item.timeSpent.inMinutes;
    }

    // Convert to percentages
    return dayGroups.map(
      (day, minutes) => MapEntry(day, (minutes / totalMinutes) * 100),
    );
  }

  /// Gets day name from weekday number
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return 'Unknown';
    }
  }

  Widget _buildSummaryStats(List<ProgressData> data) {
    final avgScore = data.isEmpty
        ? 0.0
        : data.map((d) => d.score).reduce((a, b) => a + b) / data.length;
    final totalSessions = data.length;
    final bestScore = data.isEmpty
        ? 0
        : data.map((d) => d.score).reduce((a, b) => a > b ? a : b);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem(
          'Avg Score',
          '${avgScore.toStringAsFixed(1)}%',
          Icons.trending_up,
        ),
        _buildStatItem('Sessions', totalSessions.toString(), Icons.school),
        _buildStatItem('Best Score', '$bestScore%', Icons.star),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  Color _getColorForSubject(String subject) {
    // Enhanced color mapping for various subjects and day names
    switch (subject.toLowerCase()) {
      case 'mathematics':
      case 'math':
        return Colors.blue;
      case 'science':
      case 'physics':
      case 'chemistry':
      case 'biology':
        return Colors.green;
      case 'language':
      case 'english':
      case 'literature':
        return Colors.orange;
      case 'history':
      case 'social studies':
        return Colors.purple;
      case 'monday':
        return Colors.red;
      case 'tuesday':
        return Colors.pink;
      case 'wednesday':
        return Colors.indigo;
      case 'thursday':
        return Colors.teal;
      case 'friday':
        return Colors.amber;
      case 'saturday':
        return Colors.cyan;
      case 'sunday':
        return Colors.deepOrange;
      case 'no activity':
        return Colors.grey.shade300;
      default:
        // Generate a consistent color based on subject name hash
        final hash = subject.hashCode;
        final colors = [
          Colors.blue,
          Colors.green,
          Colors.orange,
          Colors.purple,
          Colors.red,
          Colors.pink,
          Colors.indigo,
          Colors.teal,
          Colors.amber,
          Colors.cyan,
        ];
        return colors[hash.abs() % colors.length];
    }
  }

  /// Converts LearningProgress to ProgressData for chart display
  List<ProgressData> _convertLearningProgressToProgressData(
    LearningProgress learningProgress,
  ) {
    final weeklyActivity = learningProgress.stats.weeklyActivity;
    final now = DateTime.now();

    // Generate progress data for the last 7 days
    return List.generate(7, (index) {
      final date = now.subtract(Duration(days: 6 - index));
      final dayKey =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      // Get actual data from weekly activity or use calculated values
      final timeSpent = weeklyActivity[dayKey] ?? 0;
      final score = _calculateDayScore(learningProgress, date);

      return ProgressData(
        date: date,
        score: score,
        timeSpent: Duration(minutes: timeSpent),
        subject: learningProgress.subject,
      );
    });
  }

  /// Calculates a score for a specific day based on learning progress
  int _calculateDayScore(LearningProgress progress, DateTime date) {
    // Base score from overall progress
    final baseScore = (progress.overallProgress * 100).round();

    // Add variation based on recent activity
    final daysSinceLastStudy = DateTime.now()
        .difference(progress.stats.lastStudyDate)
        .inDays;
    final activityBonus = daysSinceLastStudy <= 1 ? 10 : 0;

    // Add streak bonus
    final streakBonus = progress.stats.streakDays > 5 ? 5 : 0;

    return (baseScore + activityBonus + streakBonus).clamp(0, 100);
  }

  /// Generates mock progress data as fallback
  /// TODO: Remove this method once real Firebase data integration is complete
  /// Enhanced fallback with realistic data patterns based on user activity
  List<ProgressData> _generateMockProgressData() {
    final now = DateTime.now();
    return List.generate(7, (index) {
      return ProgressData(
        date: now.subtract(Duration(days: 6 - index)),
        score:
            60 + (index * 5) + (index % 3 * 10), // Mock progressive improvement
        timeSpent: Duration(minutes: 30 + (index * 5)),
        subject: widget.subject ?? 'Mathematics',
      );
    });
  }
}

/// Data model for progress tracking
class ProgressData {
  final DateTime date;
  final int score;
  final Duration timeSpent;
  final String subject;

  ProgressData({
    required this.date,
    required this.score,
    required this.timeSpent,
    required this.subject,
  });
}
