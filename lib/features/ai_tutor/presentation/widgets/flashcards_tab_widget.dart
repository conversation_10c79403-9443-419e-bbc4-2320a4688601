import 'package:flutter/material.dart';

/// Widget for the flashcards tab
/// Extracted from the merged AI tutor page for better code organization
class FlashcardsTabWidget extends StatelessWidget {
  final VoidCallback onCreateFlashcards;
  final Map<String, dynamic> stats;

  const FlashcardsTabWidget({
    super.key,
    required this.onCreateFlashcards,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with create button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Flashcards',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: onCreateFlashcards,
                icon: const Icon(Icons.add),
                label: const Text('Create Set'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Cards',
                  '${stats['totalCards']}',
                  Icons.quiz,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Due Today',
                  '${stats['dueToday']}',
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Mastered',
                  '${stats['mastered']}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Flashcard sets list
          Expanded(child: _buildFlashcardSetsList(context)),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFlashcardSetsList(BuildContext context) {
    // TODO: Replace with actual flashcard sets from BLoC state
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.style, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No flashcard sets yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first flashcard set to start studying',
            style: TextStyle(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: onCreateFlashcards,
            icon: const Icon(Icons.add),
            label: const Text('Create Flashcard Set'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}

/// Dialog for creating flashcard sets
class CreateFlashcardsDialog extends StatefulWidget {
  final Function(String topic, int count) onCreateFlashcards;

  const CreateFlashcardsDialog({
    super.key,
    required this.onCreateFlashcards,
  });

  @override
  State<CreateFlashcardsDialog> createState() => _CreateFlashcardsDialogState();
}

class _CreateFlashcardsDialogState extends State<CreateFlashcardsDialog> {
  final TextEditingController _topicController = TextEditingController();
  final TextEditingController _countController = TextEditingController(text: '10');

  @override
  void dispose() {
    _topicController.dispose();
    _countController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Flashcard Set'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _topicController,
            decoration: const InputDecoration(
              labelText: 'Topic',
              hintText: 'e.g., Spanish Vocabulary',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _countController,
            decoration: const InputDecoration(
              labelText: 'Number of cards',
              hintText: '10',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final topic = _topicController.text.trim();
            final count = int.tryParse(_countController.text) ?? 10;
            
            if (topic.isNotEmpty) {
              widget.onCreateFlashcards(topic, count);
              Navigator.pop(context);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Please enter a topic')),
              );
            }
          },
          child: const Text('Create'),
        ),
      ],
    );
  }
}
