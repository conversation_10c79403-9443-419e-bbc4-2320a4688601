import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/ai_tutor_bloc.dart';

/// Widget for learning plan setup form
/// Extracted from the merged AI tutor page for better code organization
class LearningPlanSetupWidget extends StatefulWidget {
  final String? selectedSubject;
  final String? selectedLevel;
  final List<String> learningGoals;
  final List<String> subjects;
  final List<String> levels;
  final Function(String?) onSubjectChanged;
  final Function(String?) onLevelChanged;
  final Function(String) onGoalAdded;
  final Function(String) onGoalRemoved;
  final VoidCallback onGeneratePlan;

  const LearningPlanSetupWidget({
    super.key,
    required this.selectedSubject,
    required this.selectedLevel,
    required this.learningGoals,
    required this.subjects,
    required this.levels,
    required this.onSubjectChanged,
    required this.onLevelChanged,
    required this.onGoalAdded,
    required this.onGoalRemoved,
    required this.onGeneratePlan,
  });

  @override
  State<LearningPlanSetupWidget> createState() => _LearningPlanSetupWidgetState();
}

class _LearningPlanSetupWidgetState extends State<LearningPlanSetupWidget> {
  final TextEditingController _goalController = TextEditingController();

  @override
  void dispose() {
    _goalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 24),
          _buildQuickStartCard(),
          const SizedBox(height: 24),
          _buildFeaturesOverview(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.school,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Welcome to AI Tutor',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Your personalized learning companion powered by AI. Create custom learning plans, practice with flashcards, take adaptive quizzes, and track your progress.',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStartCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Start',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSubjectSelector(),
            const SizedBox(height: 16),
            _buildLevelSelector(),
            const SizedBox(height: 16),
            _buildGoalsInput(),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: widget.onGeneratePlan,
                icon: const Icon(Icons.auto_awesome),
                label: const Text('Generate Learning Plan'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Subject', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: widget.selectedSubject,
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            hintText: 'Select a subject',
            prefixIcon: const Icon(Icons.school),
          ),
          items: widget.subjects.map((subject) {
            return DropdownMenuItem(value: subject, child: Text(subject));
          }).toList(),
          onChanged: widget.onSubjectChanged,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a subject';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLevelSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Current Level', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: widget.levels.map((level) {
              return RadioListTile<String>(
                title: Text(level),
                value: level,
                groupValue: widget.selectedLevel,
                onChanged: widget.onLevelChanged,
                dense: true,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildGoalsInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Learning Goals', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Display existing goals as chips
              if (widget.learningGoals.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: widget.learningGoals.map((goal) {
                    return Chip(
                      label: Text(goal),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () => widget.onGoalRemoved(goal),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 8),
              ],
              // Input field for new goals
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _goalController,
                      decoration: const InputDecoration(
                        hintText: 'Add a learning goal...',
                        border: InputBorder.none,
                        isDense: true,
                      ),
                      onSubmitted: _addGoal,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: () => _addGoal(_goalController.text),
                    tooltip: 'Add goal',
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _addGoal(String goal) {
    if (goal.trim().isNotEmpty && !widget.learningGoals.contains(goal.trim())) {
      widget.onGoalAdded(goal.trim());
      _goalController.clear();
    }
  }

  Widget _buildFeaturesOverview() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Features',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              icon: Icons.map,
              title: 'Personalized Learning Plans',
              description: 'AI-generated study plans tailored to your goals and learning style.',
            ),
            _buildFeatureItem(
              icon: Icons.style,
              title: 'Smart Flashcards',
              description: 'Spaced repetition system that adapts to your memory patterns.',
            ),
            _buildFeatureItem(
              icon: Icons.quiz,
              title: 'Adaptive Quizzes',
              description: 'Dynamic quizzes that adjust difficulty based on your performance.',
            ),
            _buildFeatureItem(
              icon: Icons.trending_up,
              title: 'Progress Tracking',
              description: 'Detailed analytics to monitor your learning journey.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: Theme.of(context).colorScheme.primary, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
