# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _discoveryapis_commons:
    dependency: transitive
    description:
      name: _discoveryapis_commons
      sha256: "113c4100b90a5b70a983541782431b82168b3cae166ab130649c36eb3559d498"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.7"
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "16e298750b6d0af7ce8a3ba7c18c69c3785d11b15ec83f6dcd0ad2a0009b3cab"
      url: "https://pub.dev"
    source: hosted
    version: "76.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "214e6f07e2a44f45972e0365c7b537eaeaddb4598db0778dd4ac64b4acd3f5b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.55"
  _macros:
    dependency: transitive
    description: dart
    source: sdk
    version: "0.3.3"
  adaptive_dialog:
    dependency: "direct main"
    description:
      name: adaptive_dialog
      sha256: "760acece71b957dbab6f2071b84eb52b4b0236f20ba6cfb89446834ee92086dc"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "1f14db053a8c23e260789e9b0980fa27f2680dd640932cae5e1137cce0e46e1e"
      url: "https://pub.dev"
    source: hosted
    version: "6.11.0"
  animate_do:
    dependency: "direct main"
    description:
      name: animate_do
      sha256: "7a3162729f0ea042f9dd84da217c5bde5472ad9cef644079929d4304a5dc4ca0"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.4"
  animated_leaderboard:
    dependency: "direct main"
    description:
      name: animated_leaderboard
      sha256: "2f029d3ab8ee06222d9b8c37824c0cd12ff218b93494259ec4a652226bb84a45"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  animated_text_kit:
    dependency: "direct main"
    description:
      name: animated_text_kit
      sha256: "37392a5376c9a1a503b02463c38bc0342ef814ddbb8f9977bc90f2a84b22fa92"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  animations:
    dependency: "direct main"
    description:
      name: animations
      sha256: d3d6dcfb218225bbe68e87ccf6378bbb2e32a94900722c5f81611dad089911cb
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  ansi_styles:
    dependency: transitive
    description:
      name: ansi_styles
      sha256: "9c656cc12b3c27b17dd982b2cc5c0cfdfbdabd7bc8f3ae5e8542d9867b47ce8a"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2+1"
  ansicolor:
    dependency: transitive
    description:
      name: ansicolor
      sha256: "50e982d500bc863e1d703448afdbf9e5a72eb48840a4f766fa361ffd6877055f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  anthropic_sdk_dart:
    dependency: "direct main"
    description:
      name: anthropic_sdk_dart
      sha256: "3157e552ee092fc5852d702e879ecd5bad9941136668620d961141b209b952e3"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+1"
  any_link_preview:
    dependency: "direct main"
    description:
      name: any_link_preview
      sha256: "3135873778da5400e784ec4f304229d16f5d748276de2fce88170554907bca46"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  app_links:
    dependency: "direct main"
    description:
      name: app_links
      sha256: "433df2e61b10519407475d7f69e470789d23d593f28224c38ba1068597be7950"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.3"
  app_links_linux:
    dependency: transitive
    description:
      name: app_links_linux
      sha256: f5f7173a78609f3dfd4c2ff2c95bd559ab43c80a87dc6a095921d96c05688c81
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_links_platform_interface:
    dependency: transitive
    description:
      name: app_links_platform_interface
      sha256: "05f5379577c513b534a29ddea68176a4d4802c46180ee8e2e966257158772a3f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_links_web:
    dependency: transitive
    description:
      name: app_links_web
      sha256: af060ed76183f9e2b87510a9480e56a5352b6c249778d07bd2c95fc35632a555
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  appkit_ui_element_colors:
    dependency: transitive
    description:
      name: appkit_ui_element_colors
      sha256: c3e50f900aae314d339de489535736238627071457c4a4a2dbbb1545b4f04f22
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  archive:
    dependency: "direct main"
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  arrow_path:
    dependency: transitive
    description:
      name: arrow_path
      sha256: "07194a2f165242543e81b7fb16d86d433b52ee31adb0dc9afe9273c0a45e2d9e"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  async:
    dependency: "direct main"
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  attributed_text:
    dependency: transitive
    description:
      name: attributed_text
      sha256: fb65cf441784612544eda4d5df7a3caad56e7f673c68bf1d48d9048228375189
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: b2a26ba8b7efa1790d6460e82971fde3e398cfbe2295df9dea22f3499d2c12a7
      url: "https://pub.dev"
    source: hosted
    version: "0.1.23"
  audioplayers:
    dependency: "direct main"
    description:
      name: audioplayers
      sha256: c346ba5a39dc208f1bab55fc239855f573d69b0e832402114bf0b793622adc4d
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: de576b890befe27175c2f511ba8b742bec83765fa97c3ce4282bba46212f58e4
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: e507887f3ff18d8e5a10a668d7bedc28206b12e10b98347797257c6ae1019c3b
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "3d3d244c90436115417f170426ce768856d8fe4dfc5ed66a049d2890acfa82f9"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "6834dd48dfb7bc6c2404998ebdd161f79cd3774a7e6779e1348d54a3bfdcfaa5"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "3609bdf0e05e66a3d9750ee40b1e37f2a622c4edb796cc600b53a90a30a2ace4"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "8605762dddba992138d476f6a0c3afd9df30ac5b96039929063eceed416795c2"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  auto_route:
    dependency: "direct main"
    description:
      name: auto_route
      sha256: b83e8ce46da7228cdd019b5a11205454847f0a971bca59a7529b98df9876889b
      url: "https://pub.dev"
    source: hosted
    version: "9.2.2"
  auto_size_text:
    dependency: "direct main"
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  autotrie:
    dependency: transitive
    description:
      name: autotrie
      sha256: "55da6faefb53cfcb0abb2f2ca8636123fb40e35286bb57440d2cf467568188f8"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  avatar_glow:
    dependency: "direct main"
    description:
      name: avatar_glow
      sha256: "848d9a85065e398241d8ffae0dcc50902980c24490aef21fd4c6b6342d4c26b4"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  awesome_notifications:
    dependency: "direct main"
    description:
      name: awesome_notifications
      sha256: "0d5fa4457f2ba4e536adc3ef6af709cdcecf4a05a1f3035981e9afa2f899b2a8"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  badges:
    dependency: "direct main"
    description:
      name: badges
      sha256: a7b6bbd60dce418df0db3058b53f9d083c22cdb5132a052145dc267494df0b84
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  barcode:
    dependency: transitive
    description:
      name: barcode
      sha256: ab180ce22c6555d77d45f0178a523669db67f95856e3378259ef2ffeb43e6003
      url: "https://pub.dev"
    source: hosted
    version: "2.2.8"
  bidi:
    dependency: transitive
    description:
      name: bidi
      sha256: "9a712c7ddf708f7c41b1923aa83648a3ed44cfd75b04f72d598c45e5be287f9d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.12"
  bloc:
    dependency: "direct main"
    description:
      name: bloc
      sha256: "106842ad6569f0b60297619e9e0b1885c2fb9bf84812935490e6c5275777804e"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  bot_toast:
    dependency: "direct main"
    description:
      name: bot_toast
      sha256: "6b93030a99a98335b8827ecd83021e92e885ffc61d261d3825ffdecdd17f3bdf"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.3"
  build:
    dependency: transitive
    description:
      name: build
      sha256: cef23f1eda9b57566c81e2133d196f8e3df48f244b317368d65c5943d91148f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: "4ae2de3e1e67ea270081eaee972e1bd8f027d459f249e0f1186730784c2e7e33"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "294a2edaf4814a378725bfe6358210196f5ea37af89ecd81bfa32960113d4948"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "99d3980049739a985cf9b21f30881f46db3ebc62c5b8d5e60e27440876b1ba1e"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "74691599a5bc750dc96a6b4bfd48f7d9d66453eab04c7f4063134800d6a5c573"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.14"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "22e3aa1c80e0ada3722fe5b63fd43d9c8990759d0a2cf489c8c5d7b2bdebc021"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "28a712df2576b63c6c005c465989a348604960c0958d28be5303ba9baa841ac2"
      url: "https://pub.dev"
    source: hosted
    version: "8.9.3"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: "7c1183e361e5c8b0a0f21a28401eecdbde252441106a9816400dd4c2b2424916"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "35814b016e37fbdc91f7ae18c8caf49ba5c88501813f73ce8a07027a395e2829"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "980842f4e8e2535b8dbd3d5ca0b1f0ba66bf61d14cc3a17a9b4788a3685ba062"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  camera:
    dependency: "direct main"
    description:
      name: camera
      sha256: "26ff41045772153f222ffffecba711a206f670f5834d40ebf5eed3811692f167"
      url: "https://pub.dev"
    source: hosted
    version: "0.11.0+2"
  camera_android:
    dependency: "direct main"
    description:
      name: camera_android
      sha256: "19b7226387218864cb2388e1ad5db7db50d065222f5511254b03fc397dd21a5e"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.9+17"
  camera_android_camerax:
    dependency: transitive
    description:
      name: camera_android_camerax
      sha256: abcfa1ac32bd03116b4cfda7e8223ab391f01966e65823c064afe388550d1b3d
      url: "https://pub.dev"
    source: hosted
    version: "0.6.10+3"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: "2e4c568f70e406ccb87376bc06b53d2f5bebaab71e2fbcc1a950e31449381bcf"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.17+5"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: b3ede1f171532e0d83111fe0980b46d17f1aa9788a07a2fbed07366bbdbb9061
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb0f1107cac15a5ea6ef0a6ef71a807b9e4267c713bb93e00e92d737cc8dbd8a
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  chat_gpt_sdk:
    dependency: "direct main"
    description:
      name: chat_gpt_sdk
      sha256: a9a9eba807319af7dbe3e0cbe1807ea8a7992a776d4723ce61b8a77cf1e21f69
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  chatgpt_completions:
    dependency: "direct main"
    description:
      name: chatgpt_completions
      sha256: "670ce3bc1aeff4b69d66604368c04b97c9df18d38dc432768ec86674b63b6760"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  chewie:
    dependency: transitive
    description:
      name: chewie
      sha256: "335df378c025588aef400c704bd71f0daea479d4cd57c471c88c056c1144e7cd"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.5"
  chopper:
    dependency: "direct main"
    description:
      name: chopper
      sha256: "40899b729fb6d8969d967264b189efaf2452bc3ccf6ed0782d00f1d8a6161c31"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.3"
  chopper_generator:
    dependency: "direct dev"
    description:
      name: chopper_generator
      sha256: de438569cba1e2a2888e8d91e3c2ac60106574eea7f36823ed0334e96146328a
      url: "https://pub.dev"
    source: hosted
    version: "8.0.3"
  chromadb:
    dependency: transitive
    description:
      name: chromadb
      sha256: "6cefa48062b33dfa0b22c77ac4a0cae2d2dfbcdc1593189758a42db02d52a357"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+2"
  cli_launcher:
    dependency: transitive
    description:
      name: cli_launcher
      sha256: "5e7e0282b79e8642edd6510ee468ae2976d847a0a29b3916e85f5fa1bfe24005"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clipboard:
    dependency: "direct main"
    description:
      name: clipboard
      sha256: "2ec38f0e59878008ceca0ab122e4bfde98847f88ef0f83331362ba4521f565a9"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      sha256: ba89d4ae6ddaea0241f50a2dc1ffe36f32891f1a6bc78540f55d79c7f8ed536a
      url: "https://pub.dev"
    source: hosted
    version: "5.6.0"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: "966cfd6beb2e943f3363a7bced4476533caa55d2338ffd876855c07e5296d1d7"
      url: "https://pub.dev"
    source: hosted
    version: "6.6.0"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: "110ba7ff3322c239527e5be3c0d471a3e14fd0a870b2feff96d1e527a2eae2bb"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  cloud_functions:
    dependency: "direct main"
    description:
      name: cloud_functions
      sha256: ec363f3b0430954702369a7e898ea25e18e60199cadc8c7030af81057b35be3b
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  cloud_functions_platform_interface:
    dependency: transitive
    description:
      name: cloud_functions_platform_interface
      sha256: "1854660b9701ba2cc3fc566d18f43e4007b6e061990f0d7a1d42420eabba1c20"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.40"
  cloud_functions_web:
    dependency: transitive
    description:
      name: cloud_functions_web
      sha256: "00149357e5579a7468a9abfbd86df638b5ab0f003de0f9b6ad6659027ad013ee"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.5"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0ec10bf4a89e4c613960bf1e8b42c64127021740fb21640c29c909826a5eea3e"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.1"
  code_editor:
    dependency: "direct main"
    description:
      name: code_editor
      sha256: "491976f97328764b6a6ef7b2db287def1ee81046cc447ff150bfd0fb668a728e"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  color:
    dependency: transitive
    description:
      name: color
      sha256: ddcdf1b3badd7008233f5acffaf20ca9f5dc2cd0172b75f68f24526a5f5725cb
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: e0817759ec6d2d8e57eb234e6e57d2173931367a865850c7acea40d4b4f9c27d
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: "42657c1715d48b167930d5f34d00222ac100475f73d10162ddf43e714932f204"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  console:
    dependency: transitive
    description:
      name: console
      sha256: e04e7824384c5b39389acdd6dc7d33f3efe6b232f6f16d7626f194f6a01ad69a
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  convenient_test:
    dependency: "direct main"
    description:
      name: convenient_test
      sha256: "019863107e3d1be995183885ae93ab3e5752c8f26b1e0b9911125d95bfdb4705"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  conventional_commit:
    dependency: transitive
    description:
      name: conventional_commit
      sha256: dec15ad1118f029c618651a4359eb9135d8b88f761aa24e4016d061cd45948f2
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0+1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  convex_bottom_bar:
    dependency: "direct main"
    description:
      name: convex_bottom_bar
      sha256: ebf0f3deb1e8e99374d844fee7485d2980ec502dedfaad395c12118477933aef
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  country_code_picker:
    dependency: "direct main"
    description:
      name: country_code_picker
      sha256: fdc2ae84d0342d4a7c6ebc83fbe002a70c8231ff5734505676292fc2c2cc4433
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: e3493833ea012784c740e341952298f1cc77f1f01b1bbc3eb4eecf6984fb7f43
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  cron:
    dependency: "direct main"
    description:
      name: cron
      sha256: e1b379dec3d7967c8e521a92832a6ac97a11ea4046cbc3e584193a470e3ad8f5
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  csv:
    dependency: transitive
    description:
      name: csv
      sha256: "63ed2871dd6471193dffc52c0e6c76fb86269c00244d244297abbb355c84a86e"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  cubit_generator:
    dependency: "direct main"
    description:
      name: cubit_generator
      sha256: "1420b3ea4e57209806f8db11ac989ec45672ae5f22b7e9c649be6e8befe13d9a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  curved_navigation_bar:
    dependency: "direct main"
    description:
      name: curved_navigation_bar
      sha256: bb4ab128fcb6f4a9f0f1f72d227db531818b20218984789777f049fcbf919279
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  dart_earcut:
    dependency: transitive
    description:
      name: dart_earcut
      sha256: "41b493147e30a051efb2da1e3acb7f38fe0db60afba24ac1ea5684cee272721e"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  dart_openai:
    dependency: "direct main"
    description:
      name: dart_openai
      sha256: "853bb57fed6a71c3ba0324af5cb40c16d196cf3aa55b91d244964ae4a241ccf1"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  dart_ping:
    dependency: "direct main"
    description:
      name: dart_ping
      sha256: "2f5418d0a5c64e53486caaac78677b25725b1e13c33c5be834ce874ea18bd24f"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.1"
  dart_ping_ios:
    dependency: "direct main"
    description:
      name: dart_ping_ios
      sha256: "17df1b369331ec6c30a91a51db64ac8feed47fad71e444208de06edf2a22415f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  dart_quill_delta:
    dependency: transitive
    description:
      name: dart_quill_delta
      sha256: bddb0b2948bd5b5a328f1651764486d162c59a8ccffd4c63e8b2c5e44be1dac4
      url: "https://pub.dev"
    source: hosted
    version: "10.8.3"
  dart_rss:
    dependency: "direct main"
    description:
      name: dart_rss
      sha256: "73539d4b7153b47beef8b51763ca55dcb6fc0bb412b29e0f5e74e93fabfd1ac6"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "7856d364b589d1f08986e140938578ed36ed948581fbc3bc9aef1805039ac5ab"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.7"
  dart_webrtc:
    dependency: transitive
    description:
      name: dart_webrtc
      sha256: b34e90bc82f33c1023cf98661369c37bccd648c8a4cf882a875d9f5d8bbef694
      url: "https://pub.dev"
    source: hosted
    version: "1.5.2+hotfix.1"
  dartpip:
    dependency: "direct dev"
    description:
      name: dartpip
      sha256: "64c8d144aa0ffec09159a81f1287f3a4c9c60c5051900cc1f74256e4d372b12b"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.8"
  dartpip_solver:
    dependency: transitive
    description:
      name: dartpip_solver
      sha256: bd18c9f938835a74aa2284c3795af6e2b9869d65f8603e8a0b294ac852f47ccf
      url: "https://pub.dev"
    source: hosted
    version: "0.0.7"
  dartx:
    dependency: transitive
    description:
      name: dartx
      sha256: "8b25435617027257d43e6508b5fe061012880ddfdaa75a71d607c3de2a13d244"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  dartz:
    dependency: "direct main"
    description:
      name: dartz
      sha256: e6acf34ad2e31b1eb00948692468c30ab48ac8250e0f0df661e29f12dd252168
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  deeply:
    dependency: transitive
    description:
      name: deeply
      sha256: e056e0bfcd30e33aa52b5cb7fe5d8213fb6a024ba19ca82ca3abc65f6d356cb1
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0+1"
  desktop_webview_auth:
    dependency: transitive
    description:
      name: desktop_webview_auth
      sha256: c4dce73346a7be7243c90ac3b1a68586d9f0e2c2710e81e07d758e80a6ebd920
      url: "https://pub.dev"
    source: hosted
    version: "0.0.15"
  desktop_window:
    dependency: "direct main"
    description:
      name: desktop_window
      sha256: d0590e75a0a8f91245b439fb7f2bf0ebb62d9cd9fdc7aa0622a5d50615c46845
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  device_frame:
    dependency: transitive
    description:
      name: device_frame
      sha256: d031a06f5d6f4750009672db98a5aa1536aa4a231713852469ce394779a23d75
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  device_preview:
    dependency: "direct main"
    description:
      name: device_preview
      sha256: a694acdd3894b4c7d600f4ee413afc4ff917f76026b97ab06575fe886429ef19
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  diff_match_patch:
    dependency: transitive
    description:
      name: diff_match_patch
      sha256: "2efc9e6e8f449d0abe15be240e2c2a3bcd977c8d126cfd70598aee60af35c0a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  diffutil_dart:
    dependency: transitive
    description:
      name: diffutil_dart
      sha256: "5e74883aedf87f3b703cb85e815bdc1ed9208b33501556e4a8a5572af9845c81"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "5598aa796bbf4699afd5c67c0f5f6e2ed542afc956884b9cd58c306966efc260"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.0"
  dio_compatibility_layer:
    dependency: transitive
    description:
      name: dio_compatibility_layer
      sha256: bb7ea1dd6fe98b8f5e3d90da408802fc3abf14d4485416e882cf1b2c8fb4b209
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "33259a9276d6cea88774a0000cfae0d861003497755969c92faa223108620dc8"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  directed_graph:
    dependency: transitive
    description:
      name: directed_graph
      sha256: "82061c9ce3ac74d9c4c173fb03daac515d503a0f9f77c0010a872f8c344a4f03"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.9"
  dots_indicator:
    dependency: transitive
    description:
      name: dots_indicator
      sha256: f1599baa429936ba87f06ae5f2adc920a367b16d08f74db58c3d0f6e93bcdb5c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  download:
    dependency: "direct main"
    description:
      name: download
      sha256: d3f39f5799913f61f84a2cfac8153a32fe71b7cd3d2282702886e6190f103c6a
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  drift:
    dependency: "direct main"
    description:
      name: drift
      sha256: cc593c8acaccaf4a5750fac034e06289f572038a38391d4343739789591e6b01
      url: "https://pub.dev"
    source: hosted
    version: "2.23.0"
  drop_cap_text:
    dependency: "direct main"
    description:
      path: "."
      ref: "feat/adapt-for-deprecations"
      resolved-ref: f9829062aefa2bea73e7ece99bf850f5d43259cc
      url: "https://github.com/parlough/drop_cap_text"
    source: git
    version: "1.2.0"
  dropdown_button2:
    dependency: "direct main"
    description:
      name: dropdown_button2
      sha256: b0fe8d49a030315e9eef6c7ac84ca964250155a6224d491c1365061bc974a9e1
      url: "https://pub.dev"
    source: hosted
    version: "2.3.9"
  dropdown_search:
    dependency: "direct main"
    description:
      name: dropdown_search
      sha256: "8c96aaf8d0c0d4307804b28da73866e5a0fc94fd8e6942eceab896450aaa4999"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.1"
  dynamic_color:
    dependency: transitive
    description:
      name: dynamic_color
      sha256: eae98052fa6e2826bdac3dd2e921c6ce2903be15c6b7f8b6d8a5d49b5086298d
      url: "https://pub.dev"
    source: hosted
    version: "1.7.0"
  easy_localization:
    dependency: "direct main"
    description:
      name: easy_localization
      sha256: fa59bcdbbb911a764aa6acf96bbb6fa7a5cf8234354fc45ec1a43a0349ef0201
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  easy_logger:
    dependency: transitive
    description:
      name: easy_logger
      sha256: c764a6e024846f33405a2342caf91c62e357c24b02c04dbc712ef232bf30ffb7
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  elevenlabs_flutter_updated:
    dependency: "direct main"
    description:
      name: elevenlabs_flutter_updated
      sha256: "85a9c5c067cdb30d428877627e72d0a98219f60eeb3f7a30b57dc580ac915d34"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  email_validator:
    dependency: "direct main"
    description:
      name: email_validator
      sha256: e9a90f27ab2b915a27d7f9c2a7ddda5dd752d6942616ee83529b686fc086221b
      url: "https://pub.dev"
    source: hosted
    version: "2.1.17"
  english_words:
    dependency: "direct main"
    description:
      name: english_words
      sha256: "6a7ef6473a97bd8571b6b641d006a6e58a7c67e65fb6f3d6d1151cb46b0e983c"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  envied:
    dependency: transitive
    description:
      name: envied
      sha256: bbff9c76120e4dc5e2e36a46690cf0a26feb65e7765633f4e8d916bcd173a450
      url: "https://pub.dev"
    source: hosted
    version: "0.5.4+1"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  eva_icons_flutter:
    dependency: "direct main"
    description:
      name: eva_icons_flutter
      sha256: "6d48a10b93590ab83eb092bee5adacdeb14f3d83f527a4b9e4092c363d56e2a8"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  excel:
    dependency: "direct main"
    description:
      name: excel
      sha256: "1a15327dcad260d5db21d1f6e04f04838109b39a2f6a84ea486ceda36e468780"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  exception_templates:
    dependency: transitive
    description:
      name: exception_templates
      sha256: e9303fd16ecdb845a14b397cb93476f807dd2b4706f5ee44f708dbc95c256c33
      url: "https://pub.dev"
    source: hosted
    version: "0.2.4"
  execution_queue:
    dependency: transitive
    description:
      name: execution_queue
      sha256: "97e6300b873e345fd1b4bfa26f5480cfa21761e952321015efc1926108d3c3c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  expandable_page_view:
    dependency: "direct main"
    description:
      name: expandable_page_view
      sha256: "210dc6961cfc29f7ed42867824eb699c9a4b9b198a7c04b8bdc1c05844969dc6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.17"
  expandable_text:
    dependency: "direct main"
    description:
      name: expandable_text
      sha256: "7d03ea48af6987b20ece232678b744862aa3250d4a71e2aaf1e4af90015d76b1"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  extended_image:
    dependency: "direct main"
    description:
      name: extended_image
      sha256: "93890a88d89ce017789f6c031c32ad8d2c685f1a5c25c169550746d973ca5e44"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.9"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: "9a94ec9314aa206cfa35f16145c3cd6e2c924badcc670eaaca8a3a8063a68cd7"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.5"
  extension:
    dependency: transitive
    description:
      name: extension
      sha256: be3a6b7f8adad2f6e2e8c63c895d19811fcf203e23466c6296267941d0ff4f24
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  extra_alignments:
    dependency: "direct main"
    description:
      name: extra_alignments
      sha256: cc0425d08b3f1b3404439da939593aa3b7931c6f7f251030632e14337bf79e00
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  feedback:
    dependency: "direct main"
    description:
      name: feedback
      sha256: "26769f73de6215add72074d24e4a23542e4c02a8fd1a873e7c93da5dc9c1d362"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  fetch_api:
    dependency: transitive
    description:
      name: fetch_api
      sha256: "97f46c25b480aad74f7cc2ad7ccba2c5c6f08d008e68f95c1077286ce243d0e6"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  fetch_client:
    dependency: transitive
    description:
      name: fetch_client
      sha256: "9666ee14536778474072245ed5cba07db81ae8eb5de3b7bf4a2d1e2c49696092"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  ffi:
    dependency: "direct main"
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  ffigen:
    dependency: "direct dev"
    description:
      name: ffigen
      sha256: e0bdaa4ff30106aab68e7fa19311df4ced2035dc07be30f2e112855e8dcd3259
      url: "https://pub.dev"
    source: hosted
    version: "16.0.0"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: c904b4ab56d53385563c7c39d8e9fa9af086f91495dfc48717ad84a42c3cf204
      url: "https://pub.dev"
    source: hosted
    version: "8.1.7"
  file_selector:
    dependency: transitive
    description:
      name: file_selector
      sha256: "5019692b593455127794d5718304ff1ae15447dea286cdda9f0db2a796a1b828"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  file_selector_android:
    dependency: transitive
    description:
      name: file_selector_android
      sha256: "98ac58e878b05ea2fdb204e7f4fc4978d90406c9881874f901428e01d3b18fbc"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1+12"
  file_selector_ios:
    dependency: transitive
    description:
      name: file_selector_ios
      sha256: "94b98ad950b8d40d96fee8fa88640c2e4bd8afcdd4817993bd04e20310f45420"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.3+1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_web:
    dependency: transitive
    description:
      name: file_selector_web
      sha256: c4c0ea4224d97a60a7067eca0c8fd419e708ff830e0c83b11a48faf566cec3e7
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "8f5d2f6590d51ecd9179ba39c64f722edc15226cc93dcc8698466ad36a4a85a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  firebase_ai:
    dependency: transitive
    description:
      name: firebase_ai
      sha256: "178bdce7248e9ad5a483c3623453d3219443d203b4a5d347b0400580fff2fd6d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      sha256: "366140abb55418ea23060b779893fa997c2d8e1974a4d1cc4d9590933b65c5fd"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.6"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "8e987cf977c0c8f4ad02d9950a9b25b1a9606899f37b66a322a43af05be0246b"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.8"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "0b64ef9060d394bba3d3b4777f49ee098efeeea7b0afb04663c956de6a3da170"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.10+5"
  firebase_app_check:
    dependency: "direct main"
    description:
      name: firebase_app_check
      sha256: a1a5255b8d19e22f2d7e9ff1a1138268cd74d2c12b31dcb487612a371d7a21e9
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2+6"
  firebase_app_check_platform_interface:
    dependency: transitive
    description:
      name: firebase_app_check_platform_interface
      sha256: "26c0eca776d5d5ffb727435b71243a00e74000bd2760792921999b04564aaf59"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1+6"
  firebase_app_check_web:
    dependency: transitive
    description:
      name: firebase_app_check_web
      sha256: "889838c053f024572ba8077cb5a380ff8b224827090a1e5878f004290303ae0e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+10"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      sha256: "10cd3f00a247f33b0a5c77574011a87379432bf3fec77a500b55f2bcc30ddd8b"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.4"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      sha256: "2d15872a8899b0459fab6b4c148fd142e135acfc8a303d383d80b455e4dba7bd"
      url: "https://pub.dev"
    source: hosted
    version: "7.6.3"
  firebase_auth_web:
    dependency: "direct main"
    description:
      name: firebase_auth_web
      sha256: efba45393050ca03d992eae1d305d5fc8c0c9f5980624053512e935c23767c4f
      url: "https://pub.dev"
    source: hosted
    version: "5.14.3"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "8cfe3c900512399ce8d50fcc817e5758ff8615eeb6fa5c846a4cc47bbf6353b6"
      url: "https://pub.dev"
    source: hosted
    version: "3.13.1"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: d7253d255ff10f85cfd2adaba9ac17bae878fa3ba577462451163bd9f1d1f0bf
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: ddd72baa6f727e5b23f32d9af23d7d453d67946f380bd9c21daf474ee0f7326e
      url: "https://pub.dev"
    source: hosted
    version: "2.23.0"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: f6adb65fa3d6391a79f0e60833bb4cdc468ce0c318831c90057ee11e0909cd29
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "6635166c22c6f75f634b8e77b70fcc43b24af4cfee28f975249dbdbd9769a702"
      url: "https://pub.dev"
    source: hosted
    version: "3.8.0"
  firebase_data_connect:
    dependency: "direct main"
    description:
      name: firebase_data_connect
      sha256: b7e5f7dd3e83cb853a2c6bb174f79ae56603802e3e3fba5140785965ff060cb9
      url: "https://pub.dev"
    source: hosted
    version: "0.1.2+6"
  firebase_database:
    dependency: transitive
    description:
      name: firebase_database
      sha256: "473c25413683c1c4c8d80918efdc1a232722624bad3b6edfed9fae52b8d927c1"
      url: "https://pub.dev"
    source: hosted
    version: "11.2.0"
  firebase_database_platform_interface:
    dependency: transitive
    description:
      name: firebase_database_platform_interface
      sha256: e83241bcbe4e1bcfcbfd12d0e2ef7706af009663d291efa96bc965adb9ded25d
      url: "https://pub.dev"
    source: hosted
    version: "0.2.5+47"
  firebase_database_web:
    dependency: transitive
    description:
      name: firebase_database_web
      sha256: "9f4048132a3645f1ad528c4839a7c15ad3ff922ee7761821ea9526ffd52735b7"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6+5"
  firebase_dynamic_links:
    dependency: transitive
    description:
      name: firebase_dynamic_links
      sha256: e4984adcdeaa69f22fb60909c5ff211b9ef14d0d0f9362e4209eeef3e552b5e2
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: f41a911267727d241e6d3453718a4b0a95ac33ee0a6e086923f9cab4acd31806
      url: "https://pub.dev"
    source: hosted
    version: "0.2.7"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "151a3ee68736abf293aab66d1317ade53c88abe1db09c75a0460aebf7767bbdf"
      url: "https://pub.dev"
    source: hosted
    version: "15.1.6"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: f331ee51e40c243f90cc7bc059222dfec4e5df53125b08d31fb28961b00d2a9d
      url: "https://pub.dev"
    source: hosted
    version: "4.5.49"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: efaf3fdc54cd77e0eedb8e75f7f01c808828c64d052ddbf94d3009974e47d30f
      url: "https://pub.dev"
    source: hosted
    version: "3.9.5"
  firebase_pagination:
    dependency: "direct main"
    description:
      name: firebase_pagination
      sha256: "21d6a266453dc8c40f6e6a077ece84fa50018317a0e1749804775bbe72db2257"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  firebase_performance:
    dependency: "direct main"
    description:
      name: firebase_performance
      sha256: "33f840ac0d3693cb2db8a05e5f181882bb4e13e784927102030c907b958a5fc1"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0+11"
  firebase_performance_platform_interface:
    dependency: transitive
    description:
      name: firebase_performance_platform_interface
      sha256: "79dffbe35d75fe5ef857a06f7880432d39fcc160224051bd17e4f4f5171edb37"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+47"
  firebase_performance_web:
    dependency: transitive
    description:
      name: firebase_performance_web
      sha256: "5d804346dcc65154a4b13ea82381a00e42e49c65004114c336a89537bbf47b64"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.7+5"
  firebase_remote_config:
    dependency: "direct main"
    description:
      name: firebase_remote_config
      sha256: e377fd519e73baf5afae343da8c8a0a88b506cfbf2ea8ceb7614a8793d9a7083
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "95755bcab41c7c9c28ac92aefc1eb4c79ffcc7565e94833cb1e7a2c9320ba53f"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.47"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "09ff1f3db2120e798530354dd77c49e6596d88eadcfe366eb3a083c2dd5d7cb0"
      url: "https://pub.dev"
    source: hosted
    version: "1.7.5"
  firebase_storage:
    dependency: "direct main"
    description:
      name: firebase_storage
      sha256: "19102fafeaf99b35e0ac6911ad4fb2929e6fb1bc1d61e848514b9ad3c139d7e6"
      url: "https://pub.dev"
    source: hosted
    version: "12.3.7"
  firebase_storage_platform_interface:
    dependency: transitive
    description:
      name: firebase_storage_platform_interface
      sha256: "4f9993c1e2839b8e49a15b7346f1822ff50edb92c8db89fafa26c2eae8767a7d"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.34"
  firebase_storage_web:
    dependency: transitive
    description:
      name: firebase_storage_web
      sha256: ed689089bd4cdafcefd0d5b8a5d635a68c657c75bdad26f15d0ca50748527103
      url: "https://pub.dev"
    source: hosted
    version: "3.10.6"
  firebase_ui_auth:
    dependency: "direct main"
    description:
      name: firebase_ui_auth
      sha256: cf2cd23625f3df3c6b27e37d04132980f17d707b1adc160bddf425afe1782eed
      url: "https://pub.dev"
    source: hosted
    version: "1.16.1"
  firebase_ui_database:
    dependency: "direct main"
    description:
      name: firebase_ui_database
      sha256: "8ed86e0f0a7d62204bb20f8f532bb8518a482b34406b956df7befb1bd89f74e6"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  firebase_ui_firestore:
    dependency: "direct main"
    description:
      name: firebase_ui_firestore
      sha256: "9492b9f989457a05e21c47d006beb4cef09be1c56d0fe8df7158f9b056be63a9"
      url: "https://pub.dev"
    source: hosted
    version: "1.7.0"
  firebase_ui_localizations:
    dependency: "direct main"
    description:
      name: firebase_ui_localizations
      sha256: "01c0c872ce08d16d217490af3438c97c4fcf1187e856dd2525f892178782fc46"
      url: "https://pub.dev"
    source: hosted
    version: "1.13.1"
  firebase_ui_oauth:
    dependency: "direct main"
    description:
      name: firebase_ui_oauth
      sha256: f7a0a12e7d4f0518848709f093400796a5c7e3ef13f3498ac83793d291341d9e
      url: "https://pub.dev"
    source: hosted
    version: "1.6.1"
  firebase_ui_oauth_apple:
    dependency: "direct main"
    description:
      name: firebase_ui_oauth_apple
      sha256: "52b4608939f8421fa456286c31a4bb565ada2bb894bac8bc6164056c3e1c43c5"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  firebase_ui_oauth_facebook:
    dependency: "direct main"
    description:
      name: firebase_ui_oauth_facebook
      sha256: "0371053b9026d305fd7390fb12a37f80ef6869742bc2c310ff34c6b5b4198a40"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  firebase_ui_oauth_google:
    dependency: "direct main"
    description:
      name: firebase_ui_oauth_google
      sha256: "5149f812af862cf3ce22358ec13356e721279cb2c40c09c78b6df4d1230333c2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  firebase_ui_oauth_twitter:
    dependency: "direct main"
    description:
      name: firebase_ui_oauth_twitter
      sha256: "826dc1114a7ec48d1823da5efde30810f9ad3a88b16740aac1f8a590db3eb925"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  firebase_ui_shared:
    dependency: "direct main"
    description:
      name: firebase_ui_shared
      sha256: f1d07c130a39104d32fba1dab274b7bcb13be2bf4e652624a4ccabb58f9781f1
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  firebase_ui_storage:
    dependency: "direct main"
    description:
      name: firebase_ui_storage
      sha256: "715afb7d83b7d6765fa1ba8dc9bb242d7f644e9d962f459bd6e0b638b02b4c60"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  firebase_vertexai:
    dependency: "direct main"
    description:
      name: firebase_vertexai
      sha256: e7e8fa44dd94d805e3427add9d23c24982410edd461094ead3c32a7f9ba73ae8
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3+4"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  fl_chart:
    dependency: "direct main"
    description:
      name: fl_chart
      sha256: "74959b99b92b9eebeed1a4049426fd67c4abc3c5a0f4d12e2877097d6a11ae08"
      url: "https://pub.dev"
    source: hosted
    version: "0.69.2"
  flash_card:
    dependency: "direct main"
    description:
      name: flash_card
      sha256: d0863cc0d6b9feaf18c599fc56849c857c4d97a2ac7c5b0c833b56287c12335c
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  flat_buffers:
    dependency: transitive
    description:
      name: flat_buffers
      sha256: "380bdcba5664a718bfd4ea20a45d39e13684f5318fcd8883066a55e21f37f4c3"
      url: "https://pub.dev"
    source: hosted
    version: "23.5.26"
  flextras:
    dependency: "direct main"
    description:
      name: flextras
      sha256: e73b5c86dd9419569d2a48db470059b41b496012513e4e1bdc56ba2c661048d9
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  fluent_ui:
    dependency: "direct main"
    description:
      name: fluent_ui
      sha256: "069dc196e093864ba7252a3ed5cc4e28039f04fc9cb687f35aeaa2e2b265dd7e"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  fluro:
    dependency: "direct main"
    description:
      name: fluro
      sha256: "24d07d0b285b213ec2045b83e85d076185fa5c23651e44dae0ac6755784b97d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_adaptive_scaffold:
    dependency: "direct main"
    description:
      name: flutter_adaptive_scaffold
      sha256: "8c515a2cb8abb3a567f8e77f10b33f47bb6fcadfe31f62364e0aca36280cdf93"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  flutter_ai_toolkit:
    dependency: "direct main"
    description:
      name: flutter_ai_toolkit
      sha256: "43f73c8622aa26037a8b3345359467e3b30a2b1ded962c9b95b96b00645e9b01"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.0"
  flutter_animate:
    dependency: "direct main"
    description:
      name: flutter_animate
      sha256: "7befe2d3252728afb77aecaaea1dec88a89d35b9b1d2eea6d04479e8af9117b5"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.2"
  flutter_background:
    dependency: "direct main"
    description:
      name: flutter_background
      sha256: "8dad66e3102da2b4046cc3adcf70625578809827170bd78e36e5890c074287d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0+1"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: b594505eac31a0518bdcb4b5b79573b8d9117b193cc80cc12e17d639b10aa27a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.6"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "5e67678e479ac639069d7af1e133f4a4702311491188ff3e0227486430db0c06"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.2"
  flutter_cache_manager:
    dependency: "direct main"
    description:
      name: flutter_cache_manager
      sha256: "400b6592f16a4409a7f2bb929a9a7e38c72cceb8ffb99ee57bbf2cb2cecf8386"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.1"
  flutter_cached_pdfview:
    dependency: "direct main"
    description:
      name: flutter_cached_pdfview
      sha256: b1a3dc1cca0ac6b35bc95aeb8253208e211468eadd68829bae8f2696f3601b5a
      url: "https://pub.dev"
    source: hosted
    version: "0.4.3"
  flutter_chat_bubble:
    dependency: "direct main"
    description:
      name: flutter_chat_bubble
      sha256: ede33df7237fea3d89464c8493bfa0bee2f94f50fd097ebd30c517c3c0d4fcf9
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  flutter_chat_types:
    dependency: transitive
    description:
      name: flutter_chat_types
      sha256: e285b588f6d19d907feb1f6d912deaf22e223656769c34093b64e1c59b094fb9
      url: "https://pub.dev"
    source: hosted
    version: "3.6.2"
  flutter_chat_ui:
    dependency: "direct main"
    description:
      name: flutter_chat_ui
      sha256: "168a4231464ad00a17ea5f0813f1b58393bdd4035683ea4dc37bbe26be62891e"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.15"
  flutter_circular_text:
    dependency: "direct main"
    description:
      name: flutter_circular_text
      sha256: "8b7a090198dc5f8651d97584e02fc121b51e92f4e3141c147a63243401609021"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  flutter_code_editor:
    dependency: "direct main"
    description:
      name: flutter_code_editor
      sha256: "505ad56dcc8a7be4b782c8113574571bc4b9723499b0c1f385b3e2c3fae11f5d"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2"
  flutter_colorpicker:
    dependency: transitive
    description:
      name: flutter_colorpicker
      sha256: "969de5f6f9e2a570ac660fb7b501551451ea2a1ab9e2097e89475f60e07816ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter_context_menu:
    dependency: transitive
    description:
      name: flutter_context_menu
      sha256: "4bc1dc30ae5aa705ed99ebbeb875898c6341a6d092397a566fecd5184b392380"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  flutter_displaymode:
    dependency: "direct main"
    description:
      name: flutter_displaymode
      sha256: "42c5e9abd13d28ed74f701b60529d7f8416947e58256e6659c5550db719c57ef"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  flutter_dotenv:
    dependency: "direct main"
    description:
      name: flutter_dotenv
      sha256: b7c7be5cd9f6ef7a78429cabd2774d3c4af50e79cb2b7593e3d5d763ef95c61b
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  flutter_download_manager:
    dependency: "direct main"
    description:
      name: flutter_download_manager
      sha256: f0e604be623f2559a2dc2ccb0918e4e5224ff28a02b864ba0c27a3c6aca6c154
      url: "https://pub.dev"
    source: hosted
    version: "0.5.5"
  flutter_driver:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  flutter_facebook_auth:
    dependency: transitive
    description:
      name: flutter_facebook_auth
      sha256: fd1a6749dafbd5923585038671b63abdcedd4fe5923eb42fc154247dc5622519
      url: "https://pub.dev"
    source: hosted
    version: "6.0.4"
  flutter_facebook_auth_platform_interface:
    dependency: transitive
    description:
      name: flutter_facebook_auth_platform_interface
      sha256: "86630c4dbba1c20fba26ea9e59ad0d48f5ff59e7373cacd36f916160186f9ce9"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_facebook_auth_web:
    dependency: transitive
    description:
      name: flutter_facebook_auth_web
      sha256: "0e2960e9e23f2538e29b406495e18b047fbf233abfaed8d2b99acda17a2f7479"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  flutter_file_dialog:
    dependency: "direct main"
    description:
      name: flutter_file_dialog
      sha256: "9344b8f07be6a1b6f9854b723fb0cf84a8094ba94761af1d213589d3cb087488"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flutter_firebase_chat_core:
    dependency: "direct main"
    description:
      name: flutter_firebase_chat_core
      sha256: d177217ef740ec73f834ab386f1d2738df936850201aa87e322370d2f583c3da
      url: "https://pub.dev"
    source: hosted
    version: "1.6.8"
  flutter_form_builder:
    dependency: "direct main"
    description:
      name: flutter_form_builder
      sha256: "39aee5a2548df0b3979a83eea38468116a888341fbca8a92c4be18a486a7bb57"
      url: "https://pub.dev"
    source: hosted
    version: "9.6.0"
  flutter_gemini:
    dependency: "direct main"
    description:
      name: flutter_gemini
      sha256: b7264b1d19acc4b1a5628a0e26c0976aa1fb948f0d3243bc3510ff51e09476b7
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  flutter_gemma:
    dependency: "direct main"
    description:
      name: flutter_gemma
      sha256: "7335e0451f489d2675c9bdf7c021f90b13e32bd4311181bd5bc74ff14d5981a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.4"
  flutter_gen:
    dependency: "direct main"
    description:
      name: flutter_gen
      sha256: "4117a3ea6b26a910c715bd58abcc5a90447e70930a5b98249e94c41da9e849bb"
      url: "https://pub.dev"
    source: hosted
    version: "5.10.0"
  flutter_gen_core:
    dependency: transitive
    description:
      name: flutter_gen_core
      sha256: "3eaa2d3d8be58267ac4cd5e215ac965dd23cae0410dc073de2e82e227be32bfc"
      url: "https://pub.dev"
    source: hosted
    version: "5.10.0"
  flutter_gen_runner:
    dependency: "direct dev"
    description:
      name: flutter_gen_runner
      sha256: e74b4ead01df3e8f02e73a26ca856759dbbe8cb3fd60941ba9f4005cd0cd19c9
      url: "https://pub.dev"
    source: hosted
    version: "5.10.0"
  flutter_highlight:
    dependency: "direct main"
    description:
      name: flutter_highlight
      sha256: "7b96333867aa07e122e245c033b8ad622e4e3a42a1a2372cbb098a2541d8782c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_hooks:
    dependency: transitive
    description:
      name: flutter_hooks
      sha256: cde36b12f7188c85286fba9b38cc5a902e7279f36dd676967106c041dc9dde70
      url: "https://pub.dev"
    source: hosted
    version: "0.20.5"
  flutter_icmp_ping:
    dependency: transitive
    description:
      name: flutter_icmp_ping
      sha256: de9633cf65a8c733fae29d08a35d3d4b343620cd1d13e1bfa88eccf56696d896
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  flutter_image_slideshow:
    dependency: "direct main"
    description:
      name: flutter_image_slideshow
      sha256: ed259d96aea889b6ddc2b641891c3b721ec8ed33e160f08f2ef11acf0438984f
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  flutter_keyboard_visibility:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility
      sha256: "98664be7be0e3ffca00de50f7f6a287ab62c763fc8c762e0a21584584a3ff4f8"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: "31cd0885738e87c72d6f055564d37fabcdacee743b396b78c7636c169cac64f5"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.2"
  flutter_link_previewer:
    dependency: "direct main"
    description:
      name: flutter_link_previewer
      sha256: "007069e60f42419fb59872beb7a3cc3ea21e9f1bdff5d40239f376fa62ca9f20"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  flutter_linkify:
    dependency: transitive
    description:
      name: flutter_linkify
      sha256: "74669e06a8f358fee4512b4320c0b80e51cffc496607931de68d28f099254073"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "5398f14efa795ffb7a33e9b6a08798b26a180edac4ad7db3f231e40f82ce11e1"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: ef41ae901e7529e52934feba19ed82827b11baa67336829564aeab3129460610
      url: "https://pub.dev"
    source: hosted
    version: "18.0.1"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: "8f685642876742c941b29c32030f6f4f6dacd0e4eaecb3efbb187d6a3812ca01"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "6c5b83c86bf819cdb177a9247a3722067dd8cc6313827ce7c77a4b238a26fd52"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_map:
    dependency: "direct main"
    description:
      name: flutter_map
      sha256: "2ecb34619a4be19df6f40c2f8dce1591675b4eff7a6857bd8f533706977385da"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  flutter_markdown:
    dependency: "direct main"
    description:
      name: flutter_markdown
      sha256: "255b00afa1a7bad19727da6a7780cf3db6c3c12e68d302d85e0ff1fdf173db9e"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4+3"
  flutter_markdown_plus:
    dependency: transitive
    description:
      name: flutter_markdown_plus
      sha256: fe74214c5ac2f850d93efda290dcde3f18006e90a87caa9e3e6c13222a5db4de
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  flutter_mobx:
    dependency: "direct main"
    description:
      name: flutter_mobx
      sha256: ba5e93467866a2991259dc51cffd41ef45f695c667c2b8e7b087bf24118b50fe
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  flutter_native_splash:
    dependency: "direct main"
    description:
      name: flutter_native_splash
      sha256: "7062602e0dbd29141fb8eb19220b5871ca650be5197ab9c1f193a28b17537bc7"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  flutter_neumorphic:
    dependency: "direct main"
    description:
      name: flutter_neumorphic
      sha256: "02606d937a3ceaa497b8a7c25f3efa95188bf93d77ebf0bd6552e432db4c2ec6"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  flutter_onboarding_slider:
    dependency: "direct main"
    description:
      name: flutter_onboarding_slider
      sha256: "70d6b7c8d469abb31f66fd80c61915d7ff9c05d2c9001ec7db659702e7bc4f9d"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  flutter_painter_v2:
    dependency: "direct main"
    description:
      name: flutter_painter_v2
      sha256: "5f9589670fb2385b69daf60ad1919c4f2b9ae2c5ee5199c0172c674032020470"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  flutter_parsed_text:
    dependency: transitive
    description:
      name: flutter_parsed_text
      sha256: "529cf5793b7acdf16ee0f97b158d0d4ba0bf06e7121ef180abe1a5b59e32c1e2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  flutter_pdfview:
    dependency: "direct main"
    description:
      name: flutter_pdfview
      sha256: "2e3fa359524e9865ec25a64593b65092b4a9974c5871228c1a771300a003d150"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  flutter_picture_taker:
    dependency: transitive
    description:
      name: flutter_picture_taker
      sha256: d24d4c10e42324832b550bd59d1fe84129e860b75b4b2d57d6b398a41fd5dc9a
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  flutter_platform_widgets:
    dependency: "direct main"
    description:
      name: flutter_platform_widgets
      sha256: "84f39540cf433aa44b235b7fca6518d1bd30aa281d8196f00be60bc76cac96f4"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "615a505aef59b151b46bbeef55b36ce2b6ed299d160c51d84281946f0aa0ce0e"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.24"
  flutter_portal:
    dependency: "direct main"
    description:
      name: flutter_portal
      sha256: "4601b3dc24f385b3761721bd852a3f6c09cddd4e943dd184ed58ee1f43006257"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  flutter_quill:
    dependency: "direct main"
    description:
      name: flutter_quill
      sha256: "31ec24b83a0e5879e678f1e073aaede10d7fe155db01a7b47a257988d698d032"
      url: "https://pub.dev"
    source: hosted
    version: "10.7.7"
  flutter_quill_delta_from_html:
    dependency: transitive
    description:
      name: flutter_quill_delta_from_html
      sha256: "3809961f59b323e3ff368921c8f4a2ae559ff05a6bd132e565d7e6180db868e8"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.5"
  flutter_quiz_matcher:
    dependency: "direct main"
    description:
      name: flutter_quiz_matcher
      sha256: e1ea656a43d0ecaad1416012c7619eb5f288763c6eafdb527aa01177243bb063
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  flutter_rating_bar:
    dependency: "direct main"
    description:
      name: flutter_rating_bar
      sha256: d2af03469eac832c591a1eba47c91ecc871fe5708e69967073c043b2d775ed93
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_rating_stars:
    dependency: "direct main"
    description:
      name: flutter_rating_stars
      sha256: "09dfa831aac2e5128fe70c5a8bd63ff4463180d5ff8545f7379c5b209d22ce1d"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter_redux:
    dependency: "direct main"
    description:
      name: flutter_redux
      sha256: "3b20be9e08d0038e1452fbfa1fdb1ea0a7c3738c997734530b3c6d0bb5fcdbdc"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0"
  flutter_resizable_container:
    dependency: "direct main"
    description:
      name: flutter_resizable_container
      sha256: "3f1db9f40620692dce5473902a5a20d9ab1094e251050662b7d3ea7725919c05"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.4"
  flutter_riverpod:
    dependency: "direct main"
    description:
      name: flutter_riverpod
      sha256: "9532ee6db4a943a1ed8383072a2e3eeda041db5657cdf6d2acecf3c21ecbe7e1"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "8239210dd68bee6b0577aa4a090890342d04a136ce1c81f98ee513fc0ce891de"
      url: "https://pub.dev"
    source: hosted
    version: "5.9.3"
  flutter_shaders:
    dependency: transitive
    description:
      name: flutter_shaders
      sha256: "34794acadd8275d971e02df03afee3dee0f98dbfb8c4837082ad0034f612a3e2"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: a857de7ea701f276fd6a6c4c67ae885b60729a3449e42766bb0e655171042801
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  flutter_speed_dial:
    dependency: "direct main"
    description:
      name: flutter_speed_dial
      sha256: "698a037274a66dbae8697c265440e6acb6ab6cae9ac5f95c749e7944d8f28d41"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  flutter_spinkit:
    dependency: "direct main"
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  flutter_stable_diffusion:
    dependency: "direct main"
    description:
      name: flutter_stable_diffusion
      sha256: "6e798f7b29358709c5c3953e0191837b05a540336471b48fdba0f7133569ab15"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+2"
  flutter_stable_diffusion_core_ml:
    dependency: transitive
    description:
      name: flutter_stable_diffusion_core_ml
      sha256: "9cc313ea02f7e0fca065a9eda56e4c6a019e3349f745f029aeb4c4048014aeed"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+2"
  flutter_stable_diffusion_platform_interface:
    dependency: transitive
    description:
      name: flutter_stable_diffusion_platform_interface
      sha256: "2a1c39ff7711ad695427e160a6cbad821b4aa68d184efaee6698c84ae49d10d4"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+2"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_stripe:
    dependency: "direct main"
    description:
      name: flutter_stripe
      sha256: "06d7d8ac853470b7c9a9ed81f9fccafaf35f417a151e1b7ddcded56113aadf6f"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.0"
  flutter_stripe_web:
    dependency: "direct main"
    description:
      name: flutter_stripe_web
      sha256: c555abcef31b3ef9ec12efb7c706f8d400124a35237831a1252e01aa1af742e7
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "54900a1a1243f3c4a5506d853a2b5c2dbc38d5f27e52a52618a8054401431123"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.16"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_test_robots:
    dependency: transitive
    description:
      name: flutter_test_robots
      sha256: "3b00f2081148bde55190997c2772f934ad2f4529cbcfc4ccfa593f8ddc117a28"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.24"
  flutter_test_runners:
    dependency: transitive
    description:
      name: flutter_test_runners
      sha256: cc575117ed66a79185a26995399d7048341517a1bd21188cb43753739627832d
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  flutter_tts:
    dependency: "direct main"
    description:
      name: flutter_tts
      sha256: ece61b9eca4311f72b674ddff8ab9b1aba1b13dcf4a5ded20f612c5cc871779c
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  flutter_typeahead:
    dependency: "direct main"
    description:
      name: flutter_typeahead
      sha256: d64712c65db240b1057559b952398ebb6e498077baeebf9b0731dade62438a6d
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  flutter_vision:
    dependency: "direct main"
    description:
      name: flutter_vision
      sha256: ef1765d992ce39998c25a9d4e2eae7c8e5c4d3fc1f31742a829476fd6758ba6f
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_webrtc:
    dependency: transitive
    description:
      name: flutter_webrtc
      sha256: "6ea3a86d95b61cfe42d5715426d355b3cece6c88d0119de428d56f6c653811ce"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.11"
  flutter_widget_from_html:
    dependency: "direct dev"
    description:
      name: flutter_widget_from_html
      sha256: f3967a5b42896662efdd420b5adaf8a7d3692b0f44462a07c80e3b4c173b1a02
      url: "https://pub.dev"
    source: hosted
    version: "0.15.3"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      sha256: b1048fd119a14762e2361bd057da608148a895477846d6149109b2151d2f7abf
      url: "https://pub.dev"
    source: hosted
    version: "0.15.2"
  follow_the_leader:
    dependency: transitive
    description:
      name: follow_the_leader
      sha256: "798baf5211ca2461c8462d4c8e94f57bf989758f8204056d607eb9a20f1cf794"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4+8"
  font_awesome_flutter:
    dependency: "direct main"
    description:
      name: font_awesome_flutter
      sha256: d3a89184101baec7f4600d58840a764d2ef760fe1c5a20ef9e6b0e9b24a07a3a
      url: "https://pub.dev"
    source: hosted
    version: "10.8.0"
  freezed:
    dependency: "direct main"
    description:
      name: freezed
      sha256: "44c19278dd9d89292cf46e97dc0c1e52ce03275f40a97c5a348e802a924bf40e"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.7"
  freezed_annotation:
    dependency: "direct main"
    description:
      name: freezed_annotation
      sha256: c2e2d632dd9b8a2b7751117abcfc2b4888ecfe181bd9fca7170d9ef02e595fe2
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  fuchsia_remote_debug_protocol:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  functional_listener:
    dependency: transitive
    description:
      name: functional_listener
      sha256: "026d1bd4f66367f11d9ec9f1f1ddb42b89e4484b356972c76d983266cf82f33f"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  functions_client:
    dependency: transitive
    description:
      name: functions_client
      sha256: b410e4d609522357396cd84bb9a8f6e3a4561b5f7d3ce82267f6f1c2af42f16b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  fwfh_cached_network_image:
    dependency: transitive
    description:
      name: fwfh_cached_network_image
      sha256: "8e44226801bfba27930673953afce8af44da7e92573be93f60385d9865a089dd"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.3"
  fwfh_chewie:
    dependency: transitive
    description:
      name: fwfh_chewie
      sha256: "37bde9cedfb6dc5546176f7f0c56af1e814966cb33ec58f16c9565ed93ccb704"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.8"
  fwfh_just_audio:
    dependency: transitive
    description:
      name: fwfh_just_audio
      sha256: "38dc2c55803bd3cef33042c473e0c40b891ad4548078424641a32032f6a1245f"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.2"
  fwfh_svg:
    dependency: transitive
    description:
      name: fwfh_svg
      sha256: "550b1014d12b5528d8bdb6e3b44b58721f3fb1f65d7a852d1623a817008bdfc4"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.3"
  fwfh_url_launcher:
    dependency: transitive
    description:
      name: fwfh_url_launcher
      sha256: b9f5d55a5ae2c2c07243ba33f7ba49ac9544bdb2f4c16d8139df9ccbebe3449c
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  fwfh_webview:
    dependency: transitive
    description:
      name: fwfh_webview
      sha256: c0a8b664b642f40f4c252a0ab4e72c22dcd97c7fb3a7e50a6b4bdb6f63afca19
      url: "https://pub.dev"
    source: hosted
    version: "0.15.3"
  gal:
    dependency: "direct main"
    description:
      name: gal
      sha256: "2771519c8b29f784d5e27f4efc2667667eef51c6c47cccaa0435a8fe8aa208e4"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  gap:
    dependency: "direct main"
    description:
      name: gap
      sha256: f19387d4e32f849394758b91377f9153a1b41d79513ef7668c088c77dbc6955d
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  gcloud:
    dependency: transitive
    description:
      name: gcloud
      sha256: c63576bbd60b7dccd1548d51286e1c5b82e26b62440372a09c2aad0785d2e9f4
      url: "https://pub.dev"
    source: hosted
    version: "0.8.18"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: d2ec66329cab29cb297d51d96c067d457ca519dca8589665fa0b82ebacb7dbe4
      url: "https://pub.dev"
    source: hosted
    version: "13.0.2"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "7aefc530db47d90d0580b552df3242440a10fe60814496a979aa67aa98b1fd47"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "6154ea2682563f69fc0125762ed7e91e7ed85d0b9776595653be33918e064807"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.8+1"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "386ce3d9cce47838355000070b1d0b13efb5bc430f8ecda7e9238c8409ace012"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "2ed69328e05cd94e7eb48bb0535f5fc0c0c44d1c4fa1e9737267484d05c29b5e"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "53da08937d07c24b0d9952eb57a3b474e29aae2abf9dd717f7e1230995f13f0e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.6"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: d85128a5dae4ea777324730dc65edd9c9f43155c109d5cc0a69cab74139fbac1
      url: "https://pub.dev"
    source: hosted
    version: "7.7.0"
  get_it_mixin:
    dependency: "direct main"
    description:
      name: get_it_mixin
      sha256: "0ab5c9f3cdaab813ec396de5d43ee3833c418424b3a99bec0071fcbf693c0bad"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  getwidget:
    dependency: "direct main"
    description:
      name: getwidget
      sha256: "064b034e0bd2becdac3ddc51a053f26435a2c7679a49012a4b66757ef065ccb3"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  ggml_library_flutter:
    dependency: "direct main"
    description:
      name: ggml_library_flutter
      sha256: ba8669b1c20682485a035e55cb3cebc4a94028044f70cda6cf14080344c889db
      url: "https://pub.dev"
    source: hosted
    version: "0.0.10"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  globbing:
    dependency: transitive
    description:
      name: globbing
      sha256: "4f89cfaf6fa74c9c1740a96259da06bd45411ede56744e28017cc534a12b6e2d"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  go_router:
    dependency: "direct main"
    description:
      name: go_router
      sha256: "2fd11229f59e23e967b0775df8d5948a519cd7e1e8b6e849729e010587b46539"
      url: "https://pub.dev"
    source: hosted
    version: "14.6.2"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: b1ac0fe2832c9cc95e5e88b57d627c5e68c223b9657f4b96e1487aa9098c7b82
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_generative_ai:
    dependency: "direct main"
    description:
      name: google_generative_ai
      sha256: "81dae159c89e4d9bdc46955b6f4ee5ae0a291f9e8f990d76f43944e0d6041d4f"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.6"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "55580f436822d64c8ff9a77e37d61f5fb1e6c7ec9d632a43ee324e2a05c3c6c9"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "4d6e199c561ca06792c964fa24b2bac7197bf4b401c2e1d23e345e5f9939f531"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.1"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      sha256: "209856c8e5571626afba7182cf634b2910069dc567954e76ec3e3fb37f5e9db3"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: bccf64ccbb2ea672dc62a61177b315a340af86b0228564484b023657544a3fd5
      url: "https://pub.dev"
    source: hosted
    version: "2.14.11"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: "6f798adb0aa1db5adf551f2e39e24bd06c8c0fbe4de912fb2d9b5b3f48147b02"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.2"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: a951981c22d790848efb9f114f81794945bc5c06bc566238a419a92f110af6cb
      url: "https://pub.dev"
    source: hosted
    version: "2.9.5"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: ff39211bd25d7fad125d19f757eba85bd154460907cd4d135e07e3d0f98a4130
      url: "https://pub.dev"
    source: hosted
    version: "0.5.10"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      sha256: fad6ddc80c427b0bba705f2116204ce1173e09cf299f85e053d57a55e5b2dd56
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: "3b96f9b6cf61915f73cbe1218a192623e296a9b8b31965702503649477761e36"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.34"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: "83f015169102df1ab2905cf8abd8934e28f87db9ace7a5fa676998842fed228a"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.8"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: "1f6e5787d7a120cc0359ddf315c92309069171306242e181c09472d1b00a2971"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: ada595df6c30cead48e66b1f3a050edf0c5cf2ba60c185d69690e08adcc6281b
      url: "https://pub.dev"
    source: hosted
    version: "0.12.4+3"
  google_static_maps_controller:
    dependency: "direct main"
    description:
      name: google_static_maps_controller
      sha256: "6ed8831664eecb3869b3144a90236d5d46acafd8aabf9ac2f8dee5fa374f493e"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  googleapis:
    dependency: "direct main"
    description:
      name: googleapis
      sha256: "864f222aed3f2ff00b816c675edf00a39e2aaf373d728d8abec30b37bee1a81c"
      url: "https://pub.dev"
    source: hosted
    version: "13.2.0"
  googleapis_auth:
    dependency: transitive
    description:
      name: googleapis_auth
      sha256: befd71383a955535060acde8792e7efc11d2fccd03dd1d3ec434e85b68775938
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  gotrue:
    dependency: transitive
    description:
      name: gotrue
      sha256: "04a6efacffd42773ed96dc752f19bb20a1fbc383e81ba82659072b775cf62912"
      url: "https://pub.dev"
    source: hosted
    version: "2.12.0"
  gradient_borders:
    dependency: transitive
    description:
      name: gradient_borders
      sha256: b1cd969552c83f458ff755aa68e13a0327d09f06c3f42f471b423b01427f21f8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  graphite:
    dependency: "direct main"
    description:
      name: graphite
      sha256: "33c807dd04bf552346e0519db9188e1964827039dbcd69415168ac53da92955e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  graphview:
    dependency: "direct main"
    description:
      name: graphview
      sha256: bdba183583b23c30c71edea09ad5f0beef612572d3e39e855467a925bd08392f
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  grouped_list:
    dependency: "direct main"
    description:
      name: grouped_list
      sha256: c52551bc17699e304634d4653b824a1aa7c6b1d3a2c1a0da1a80839f867353fb
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  grpc:
    dependency: transitive
    description:
      name: grpc
      sha256: e93ee3bce45c134bf44e9728119102358c7cd69de7832d9a874e2e74eb8cab40
      url: "https://pub.dev"
    source: hosted
    version: "3.2.4"
  gtk:
    dependency: transitive
    description:
      name: gtk
      sha256: e8ce9ca4b1df106e4d72dad201d345ea1a036cc12c360f1a7d5a758f78ffa42c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  hashcodes:
    dependency: transitive
    description:
      name: hashcodes
      sha256: "80f9410a5b3c8e110c4b7604546034749259f5d6dcca63e0d3c17c9258f1a651"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  highlight:
    dependency: transitive
    description:
      name: highlight
      sha256: "5353a83ffe3e3eca7df0abfb72dcf3fa66cc56b953728e7113ad4ad88497cf21"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  highlighter:
    dependency: "direct main"
    description:
      name: highlighter
      sha256: "92180c72b9da8758e1acf39a45aa305a97dcfe2fdc8f3d1d2947c23f2772bfbc"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  hive:
    dependency: "direct main"
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  home_widget:
    dependency: "direct main"
    description:
      name: home_widget
      sha256: b313e3304c0429669fddf1286e1fbf61a64b873f38ba30b3eb890ef0d7560b12
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  hooks_riverpod:
    dependency: "direct main"
    description:
      name: hooks_riverpod
      sha256: "70bba33cfc5670c84b796e6929c54b8bc5be7d0fe15bb28c2560500b9ad06966"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "1fc58edeaec4307368c60d59b7e15b9d658b57d7f3125098b6294153c75337ec"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.5"
  html2md:
    dependency: "direct main"
    description:
      name: html2md
      sha256: "465cf8ffa1b510fe0e97941579bf5b22e2d575f2cecb500a9c0254efe33a8036"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: fe7ab022b76f3034adc518fb6ea04a82387620e19977665ea18d30a1cf43442f
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  http2:
    dependency: transitive
    description:
      name: http2
      sha256: "382d3aefc5bd6dc68c6b892d7664f29b5beb3251611ae946a98d35158a82bbfa"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: aa6199f908078bb1c5efb8d8638d4ae191aac11b311132c3ef48ce352fb52ef8
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "76d306a1c3afb33fe82e2bbacad62a61f409b5634c915fceb0d799de1a913360"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  hydrated_bloc:
    dependency: "direct main"
    description:
      name: hydrated_bloc
      sha256: af35b357739fe41728df10bec03aad422cdc725a1e702e03af9d2a41ea05160c
      url: "https://pub.dev"
    source: hosted
    version: "9.1.5"
  icons_launcher:
    dependency: "direct main"
    description:
      name: icons_launcher
      sha256: a7c83fbc837dc6f81944ef35c3756f533bb2aba32fcca5cbcdb2dbcd877d5ae9
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  image_editor:
    dependency: "direct main"
    description:
      name: image_editor
      sha256: "38070067264fd9fea4328ca630d2ff7bd65ebe6aa4ed375d983b732d2ae7146b"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  image_editor_common:
    dependency: transitive
    description:
      name: image_editor_common
      sha256: "93d2f5c8b636f862775dd62a9ec20d09c8272598daa02f935955a4640e1844ee"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  image_editor_ohos:
    dependency: transitive
    description:
      name: image_editor_ohos
      sha256: "06756859586d5acefec6e3b4f356f9b1ce05ef09213bcb9a0ce1680ecea2d054"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.9"
  image_editor_platform_interface:
    dependency: transitive
    description:
      name: image_editor_platform_interface
      sha256: "474517efc770464f7d99942472d8cfb369a3c378e95466ec17f74d2b80bd40de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  image_fade:
    dependency: "direct main"
    description:
      name: image_fade
      sha256: "7296c9c53cd5de98e675ef1e27bdaa4035d6c3a45cf5b86094b2e545689b4ea6"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.2"
  image_input:
    dependency: "direct main"
    description:
      name: image_input
      sha256: "50c197fc84e96b6e7e51490d2226dd8700e51f859ef2aa11546f73abdd46376a"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  image_painter:
    dependency: "direct main"
    description:
      name: image_painter
      sha256: "0db91550562821d38e54b5960dbb3a7438d7ae3b48733dcee5fffb964b9bf95c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: aa6f1280b670861ac45220cc95adc59bb6ae130259d36f980ccb62220dc5e59f
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+19"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "4f0568120c6fcc0aaa04511cb9f9f4d29fc3d0139884b1d06be88dcec7641d6b"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+1"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_size_getter:
    dependency: transitive
    description:
      name: image_size_getter
      sha256: "9a299e3af2ebbcfd1baf21456c3c884037ff524316c97d8e56035ea8fdf35653"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  in_app_purchase:
    dependency: "direct main"
    description:
      name: in_app_purchase
      sha256: "960f26a08d9351fb8f89f08901f8a829d41b04d45a694b8f776121d9e41dcad6"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  in_app_purchase_android:
    dependency: transitive
    description:
      name: in_app_purchase_android
      sha256: fa1befc0e25374d16ca532154546503f25a865ed6e7ea5641f4f2b75b092d669
      url: "https://pub.dev"
    source: hosted
    version: "0.3.6+12"
  in_app_purchase_platform_interface:
    dependency: transitive
    description:
      name: in_app_purchase_platform_interface
      sha256: "1d353d38251da5b9fea6635c0ebfc6bb17a2d28d0e86ea5e083bf64244f1fb4c"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  in_app_purchase_storekit:
    dependency: transitive
    description:
      name: in_app_purchase_storekit
      sha256: "250b3865da1f4abf0d0e1eda9556734ee22ab5ebfe47a59227d8d9aeaa1c03e8"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.20+2"
  in_app_review:
    dependency: "direct main"
    description:
      name: in_app_review
      sha256: "36a06771b88fb0e79985b15e7f2ac0f1142e903fe72517f3c055d78bc3bc1819"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.10"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: fed2c755f2125caa9ae10495a3c163aa7fab5af3585a9c62ef4a6920c5b45f10
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  infinite_canvas:
    dependency: "direct main"
    description:
      name: infinite_canvas
      sha256: "0e479626b33f4ab6f93572f80e717047981f40d3cc16904be3bc11239977743a"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.10"
  infinite_scroll_pagination:
    dependency: "direct main"
    description:
      name: infinite_scroll_pagination
      sha256: "4047eb8191e8b33573690922a9e995af64c3949dc87efc844f936b039ea279df"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  injectable:
    dependency: "direct main"
    description:
      name: injectable
      sha256: "5e1556ea1d374fe44cbe846414d9bab346285d3d8a1da5877c01ad0774006068"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  injectable_generator:
    dependency: "direct dev"
    description:
      name: injectable_generator
      sha256: af403d76c7b18b4217335e0075e950cd0579fd7f8d7bd47ee7c85ada31680ba1
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  injector:
    dependency: transitive
    description:
      name: injector
      sha256: ed389bed5b48a699d5b9561c985023d0d5cc88dd5ff2237aadcce5a5ab433e4e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  integration_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  internet_connection_checker:
    dependency: "direct main"
    description:
      name: internet_connection_checker
      sha256: ee08f13d8b13b978affe226e9274ca3ba7a9bed07c9479e8ae245f785b7a488a
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  intersperse:
    dependency: transitive
    description:
      name: intersperse
      sha256: "2f8a905c96f6cbba978644a3d5b31b8d86ddc44917662df7d27a61f3df66a576"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  intl_translation:
    dependency: "direct main"
    description:
      name: intl_translation
      sha256: b3f1ebfab4109d1a946b45c57523628da92a0e2a2df5f2d9981ef4334fd24a26
      url: "https://pub.dev"
    source: hosted
    version: "0.20.1"
  intro_slider:
    dependency: "direct main"
    description:
      name: intro_slider
      sha256: eb43fefa27b0655edebc3e7fe7ff320cb5996a4e64649d79969c7099105fe52f
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  introduction_screen:
    dependency: "direct main"
    description:
      name: introduction_screen
      sha256: "325f26e86fa3c3e86e6ab2bbc1fda860c9e6eae5ff29166fc2a3cab8f710d5b5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.14"
  io:
    dependency: transitive
    description:
      name: io
      sha256: dfd5a80599cf0165756e3181807ed3e77daf6dd4137caaad72d0b7931597650b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  isolate_contactor:
    dependency: transitive
    description:
      name: isolate_contactor
      sha256: "6ba8434ceb58238a1389d6365111a3efe7baa1c68a66f4db6d63d351cf6c3a0f"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  isolate_manager:
    dependency: transitive
    description:
      name: isolate_manager
      sha256: "22ed0c25f80ec3b5f21e3a55d060f4650afff33f27c2dff34c0f9409d5759ae5"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.5+1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: c1b2e9b5ea78c45e1a0788d29606ba27dc5f71f019f32ca5140f61ef071838cf
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: c2fcb3920cf2b6ae6845954186420fca40bc0a8abcc84903b7801f17d7050d7c
      url: "https://pub.dev"
    source: hosted
    version: "6.9.0"
  just_audio:
    dependency: "direct main"
    description:
      name: just_audio
      sha256: a49e7120b95600bd357f37a2bb04cd1e88252f7cdea8f3368803779b925b1049
      url: "https://pub.dev"
    source: hosted
    version: "0.9.42"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: "0243828cce503c8366cc2090cefb2b3c871aa8ed2f520670d76fd47aa1ab2790"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "9a98035b8b24b40749507687520ec5ab404e291d2b0937823ff45d92cb18d448"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.13"
  jwt_decode:
    dependency: transitive
    description:
      name: jwt_decode
      sha256: d2e9f68c052b2225130977429d30f187aa1981d789c76ad104a32243cfdebfbb
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  lan_scanner:
    dependency: "direct main"
    description:
      name: lan_scanner
      sha256: a36865c20469f99d26078b488d4ce2787fef8cf59c4c5c5f2f72a148c6721418
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0+1"
  langchain:
    dependency: "direct main"
    description:
      name: langchain
      sha256: "5ed0d1718108f78657f6e83dc7ce383f054a9ad46c7230957d82600ca677deaa"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.7+2"
  langchain_anthropic:
    dependency: "direct main"
    description:
      name: langchain_anthropic
      sha256: "0379ef48ef5506c2b1b4c3b20216cd0f878b76822523a2c06abff1cd5b1b59eb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+1"
  langchain_chroma:
    dependency: "direct main"
    description:
      name: langchain_chroma
      sha256: b419be4c57adcf612f89af69fdb7adf693c1f241fa50e153e588402a43bb2257
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+5"
  langchain_core:
    dependency: transitive
    description:
      name: langchain_core
      sha256: "89ae8945acfc21434e6374324a4592e7cfe49a3af28a6d9780f8c9cf5028fe31"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.6+1"
  langchain_firebase:
    dependency: "direct main"
    description:
      name: langchain_firebase
      sha256: "7030b84e2ba9a894e8d4805fff34ba344c67b0fafd81f70365d208a8a6f63bc7"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+4"
  langchain_google:
    dependency: "direct main"
    description:
      name: langchain_google
      sha256: "433a4c74feacc228f01ea46bb96092eaa3e46c558c2455d3a5dd79d85af4748f"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.4+2"
  langchain_mistralai:
    dependency: "direct main"
    description:
      name: langchain_mistralai
      sha256: "26544c1d3b084b5a86c0ddcd93ba69f7147ec03451fc5b5bb87365c7ef7d3da4"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3+2"
  langchain_ollama:
    dependency: "direct main"
    description:
      name: langchain_ollama
      sha256: "96909699321867fafd17c5999f25140bdc0bc0183d69cab1e3cf58c34fa0edf7"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2+2"
  langchain_openai:
    dependency: "direct main"
    description:
      name: langchain_openai
      sha256: b8d1b5797c9ac003d0a6e68149cfe5df8a239c3fc079b81b5dc0c03a2fd37723
      url: "https://pub.dev"
    source: hosted
    version: "0.7.3"
  langchain_tiktoken:
    dependency: "direct main"
    description:
      name: langchain_tiktoken
      sha256: c1804f4b3e56574ca67e562305d9f11e3eabe3c8aa87fea8635992f7efc66674
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  langgraph:
    dependency: "direct main"
    description:
      name: langgraph
      sha256: "216e6ac4743d1464ac81134a1ea9e7cda9d0f104483a67716cba349b24fbb9c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1-dev.1"
  latlong2:
    dependency: "direct main"
    description:
      name: latlong2
      sha256: "98227922caf49e6056f91b6c56945ea1c7b166f28ffcd5fb8e72fc0b453cc8fe"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  lazy_memo:
    dependency: transitive
    description:
      name: lazy_memo
      sha256: ca4be412d65726f79a30ba6e40b7056f1dbefbb35ec42913365b3f3e86d49279
      url: "https://pub.dev"
    source: hosted
    version: "0.1.9"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  linked_scroll_controller:
    dependency: transitive
    description:
      name: linked_scroll_controller
      sha256: e6020062bcf4ffc907ee7fd090fa971e65d8dfaac3c62baf601a3ced0b37986a
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  linkify:
    dependency: transitive
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  lint:
    dependency: transitive
    description:
      name: lint
      sha256: "4a539aa34ec5721a2c7574ae2ca0336738ea4adc2a34887d54b7596310b33c85"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: c35bb79562d980e9a453fc715854e1ed39e24e7d0297a880ef54e17f9874a9d7
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  lists:
    dependency: transitive
    description:
      name: lists
      sha256: "4ca5c19ae4350de036a7e996cdd1ee39c93ac0a2b840f4915459b7d0a7d4ab27"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  livekit_client:
    dependency: "direct main"
    description:
      name: livekit_client
      sha256: e1e2524c8b0c9280ab852b5239b9ad00f10f50c82c5f6cc674df3583b6d3408b
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1+hotfix.1"
  llama_library_flutter:
    dependency: "direct main"
    description:
      name: llama_library_flutter
      sha256: "4a19c796a697fe68569f3c37ae76ced57f8cc63824a510bf0ec3ea580219e86d"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  llama_sdk:
    dependency: "direct main"
    description:
      name: llama_sdk
      sha256: "23fcec0a0992497869728d54f14138a852ebcd4f6bda3abd4fe900b0d1b44e23"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "434d854cf478f17f12ab29a76a02b3067f86a63a6d6c4eb8fbfdcfe4879c1b7b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: "6763aaf8965f21822624cb2fd3c03d2a8b3791037b5efb0fe4b13e110f5afc92"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.46"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "5c5127061107278ab4cafa1ac51b3b6760282bf1a2abf011270908a429d1634b"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "1b842ff177a7068442eae093b64abe3592f816afd2a533c0ebcdbe40f9d2075a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: bc4e66a29b0fdf751aafbec923b5bed7ad6ed3614875d8151afe2578520b2ab5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  location:
    dependency: "direct main"
    description:
      name: location
      sha256: "37ffdadcd4b1498b769824f45ebb4de8ed46663a4a67ac27b33a590ee486579f"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  location_platform_interface:
    dependency: transitive
    description:
      name: location_platform_interface
      sha256: "2ecde6bb0f88032b0bbbde37e18975b4468711dd92619c2235cc0c0ee93b4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  location_web:
    dependency: transitive
    description:
      name: location_web
      sha256: "924da8436db7ded5eef92a7ef3ae6aa3715831a93965376c91738f586302350e"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  logger:
    dependency: "direct main"
    description:
      name: logger
      sha256: be4b23575aac7ebf01f225a241eb7f6b5641eeaf43c6a8613510fc2f8cf187d1
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  logging:
    dependency: "direct main"
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "377d87b8dcef640c04717e93afb86a510f0e1117a399ab94dc4b3f39c85eaa87"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  macos_ui:
    dependency: transitive
    description:
      name: macos_ui
      sha256: c524398d3db161ac52254ebc9617c3399bdcf269ce7a70e014f202b5c4da5ea4
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  macos_window_utils:
    dependency: transitive
    description:
      name: macos_window_utils
      sha256: "3534f2af024f2f24112ca28789a44e6750083f8c0065414546c6593ee48a5009"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.1"
  macros:
    dependency: transitive
    description:
      name: macros
      sha256: "1d9e801cd66f7ea3663c45fc708450db1fa57f988142c64289142c9b7ee80656"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3-main.0"
  maps_launcher:
    dependency: "direct main"
    description:
      name: maps_launcher
      sha256: dac4c609720211fa6336b5903d917fe45e545c6b5665978efc3db2a3f436b1ae
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0+1"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: "935e23e1ff3bc02d390bad4d4be001208ee92cc217cb5b5a6c19bc14aaa318c1"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  markdown_quill:
    dependency: "direct main"
    description:
      name: markdown_quill
      sha256: d9b070ad2356d4dfcbc7ab602c77c8c253714d886d0098e2379884036aa42061
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  markdown_widget:
    dependency: "direct main"
    description:
      name: markdown_widget
      sha256: "216dced98962d7699a265344624bc280489d739654585ee881c95563a3252fac"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2+6"
  mason:
    dependency: "direct main"
    description:
      name: mason
      sha256: "4439bb2902b24298ff5c8e4d173afd7c665d9837ae0324608e7d2963c37e7928"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  mason_logger:
    dependency: transitive
    description:
      name: mason_logger
      sha256: b6d6d159927a4165f197ffc5993ea680dd41c59daf35bff23bae28390c09a36e
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  material_design_icons_flutter:
    dependency: "direct main"
    description:
      name: material_design_icons_flutter
      sha256: "6f986b7a51f3ad4c00e33c5c84e8de1bdd140489bbcdc8b66fc1283dad4dea5a"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.7296"
  material_symbols_icons:
    dependency: "direct main"
    description:
      name: material_symbols_icons
      sha256: "64404f47f8e0a9d20478468e5decef867a688660bad7173adcd20418d7f892c9"
      url: "https://pub.dev"
    source: hosted
    version: "4.2801.0"
  math_expressions:
    dependency: "direct main"
    description:
      name: math_expressions
      sha256: e32d803d758ace61cc6c4bdfed1226ff60a6a23646b35685670d28b5616139f8
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  melos:
    dependency: "direct dev"
    description:
      name: melos
      sha256: a62abfa8c7826cec927f8585572bb9adf591be152150494d879ca2c75118809d
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  memory_cache:
    dependency: "direct main"
    description:
      name: memory_cache
      sha256: "33cb7d3330a67c87fa61441400317d46eb011041aded7e5279bb5f675cf981bb"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mgrs_dart:
    dependency: transitive
    description:
      name: mgrs_dart
      sha256: fb89ae62f05fa0bb90f70c31fc870bcbcfd516c843fb554452ab3396f78586f7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "41a20518f0cb1256669420fdba0cd90d21561e560ac240f26ef8322e45bb7ed6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  mind_map:
    dependency: "direct main"
    description:
      name: mind_map
      sha256: "42f95cf7a7bf7b1771c04d29d740cd6331a15b609ea20637e6c92fd8fce48c43"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  mirai:
    dependency: "direct dev"
    description:
      name: mirai
      sha256: "2539b1e61d6e2d7029e02a1a49cf188522817eb91c56008979d572feaa0ef90d"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  mirai_framework:
    dependency: transitive
    description:
      name: mirai_framework
      sha256: "5f6590af8a9221c326590e0c29513d7032a3b94bfe7ae3820bd4c5b144083335"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  mistralai_dart:
    dependency: transitive
    description:
      name: mistralai_dart
      sha256: "8ebdf3d12d767691b8cfba89333174b802e83346a09517e9459453ddc86272a8"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3+4"
  ml_algo:
    dependency: "direct main"
    description:
      name: ml_algo
      sha256: "52c4f23eb3184848fdf252d0344e6e6b4e5fd6bdc0fdbbbcbc4c6f0347538c43"
      url: "https://pub.dev"
    source: hosted
    version: "16.17.13"
  ml_dataframe:
    dependency: transitive
    description:
      name: ml_dataframe
      sha256: "75434865e7ff85edcf8b006cb7c580e65a7863bb853010f41e12597b69d55db7"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  ml_linalg:
    dependency: "direct main"
    description:
      name: ml_linalg
      sha256: abbbd506dc894f8b0394cf5c5d45aab45b7e2a89b86bba9c9b5b8a412b4bec5d
      url: "https://pub.dev"
    source: hosted
    version: "13.12.6"
  ml_preprocessing:
    dependency: transitive
    description:
      name: ml_preprocessing
      sha256: fdc7bdf65e53bf377db2ac6ad017d88f08d81827e1c6062b9348893c0bab9f26
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  mobx:
    dependency: "direct main"
    description:
      name: mobx
      sha256: bf1a90e5bcfd2851fc6984e20eef69557c65d9e4d0a88f5be4cf72c9819ce6b0
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  mockito:
    dependency: "direct main"
    description:
      name: mockito
      sha256: f99d8d072e249f719a5531735d146d8cf04c580d93920b04de75bef6dfb2daf6
      url: "https://pub.dev"
    source: hosted
    version: "5.4.5"
  mocktail:
    dependency: transitive
    description:
      name: mocktail
      sha256: "890df3f9688106f25755f26b1c60589a92b3ab91a22b8b224947ad041bf172d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  modal_bottom_sheet:
    dependency: "direct main"
    description:
      name: modal_bottom_sheet
      sha256: eac66ef8cb0461bf069a38c5eb0fa728cee525a531a8304bd3f7b2185407c67e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  msix:
    dependency: "direct dev"
    description:
      name: msix
      sha256: c50d6bd1aafe0d071a3c1e5a5ccb056404502935cb0a549e3178c4aae16caf33
      url: "https://pub.dev"
    source: hosted
    version: "3.16.8"
  mustache_template:
    dependency: transitive
    description:
      name: mustache_template
      sha256: a46e26f91445bfb0b60519be280555b06792460b27b19e2b19ad5b9740df5d1c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  network_info_plus:
    dependency: "direct main"
    description:
      name: network_info_plus
      sha256: "4601b815b1c6a46d84839f65cd774a7d999738471d910fae00d813e9e98b04e1"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0+1"
  network_info_plus_platform_interface:
    dependency: transitive
    description:
      name: network_info_plus_platform_interface
      sha256: "881f5029c5edaf19c616c201d3d8b366c5b1384afd5c1da5a49e4345de82fb8b"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "6e7eac89047ab8a8d26cf16127b5ed26de65209847630400f9aefd7cd5c730db"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  objectbox:
    dependency: "direct main"
    description:
      name: objectbox
      sha256: ea823f4bf1d0a636e7aa50b43daabb64dd0fbd80b85a033016ccc1bc4f76f432
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  objectdb:
    dependency: "direct main"
    description:
      name: objectdb
      sha256: a6225fdc2ea77feb30d87713a2999e7bbf86a7eebdcc9451932e3b1cbb5e2f1b
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1+1"
  objectid:
    dependency: transitive
    description:
      name: objectid
      sha256: "22fa972000d3256f10d06323a9dcbf4b564fb03fdb9024399e3a6c1d9902f914"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  ollama:
    dependency: "direct main"
    description:
      name: ollama
      sha256: "653a434cc7695dc2243975434015f4831f4ce524cccc1cc8fdb2d31551e3867b"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  ollama_dart:
    dependency: "direct main"
    description:
      name: ollama_dart
      sha256: "4e40bc499b6fe46ba54a004d2da601c40bd73d66e3f18cf7b03225ccf3d481a6"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2+1"
  onnxruntime:
    dependency: "direct main"
    description:
      name: onnxruntime
      sha256: e77ec05acafc135cc5fe7bcdf11b101b39f06513c9d5e9fa02cb1929f6bac72a
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  openai_dart:
    dependency: transitive
    description:
      name: openai_dart
      sha256: "1cc5ed0915fa7572b943de01cfa7a3e5cfe1e6a7f4d0d9a9374d046518e84575"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.5"
  openai_realtime_dart:
    dependency: "direct main"
    description:
      name: openai_realtime_dart
      sha256: "75215c0b9dca47630052329c75dfb73e21040af2a68cfff1788428d41a42d890"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3+1"
  os_detect:
    dependency: transitive
    description:
      name: os_detect
      sha256: e704fb99aa30b2b9a284d87a28eef9ba262f68c25c963d5eb932f54cad07784f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  outetts_flutter:
    dependency: "direct main"
    description:
      name: outetts_flutter
      sha256: "705b862f5a1ab7a0b037e8ee48eef0cec504b225fb7f4a445635efcd511680e5"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  overlord:
    dependency: transitive
    description:
      name: overlord
      sha256: "576256bc9ce3fb0ae3042bbb26eed67bdb26a5045dd7e3c851aae65b0bbab2f5"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3+5"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "92d4488434b520a62570293fbd33bb556c7d49230791c1b4bbd973baf6d2dc67"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "70c421fe9d9cc1a9a7f3b05ae56befd469fe4f8daa3b484823141a55442d858d"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.2"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: a5ef9986efc7bf772f2696183a3992615baa76c1ffb1189318dd8803778fb05b
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  page_transition:
    dependency: "direct main"
    description:
      name: page_transition
      sha256: "9d2a780d7d68b53ae82fbcc43e06a16195e6775e9aae40e55dc0cbb593460f9d"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  painter:
    dependency: "direct main"
    description:
      name: painter
      sha256: "8919468e313a5d8ded41ee1e6db8dec72dd85359174b7866feef8fbc82c603cc"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  particle_field:
    dependency: "direct main"
    description:
      name: particle_field
      sha256: "9a5ebdde32751f82aba64198e9bbd46738e789e275bf9e7a88318ed460be560e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "4adf4fd5423ec60a29506c76581bc05854c55e3a0b72d35bb28d661c9686edf2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.15"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  pay:
    dependency: "direct main"
    description:
      name: pay
      sha256: a152e5c26e7cd27312372a8ac6e572e54d2e9408d6842e64ec7930c5a1838812
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  pay_android:
    dependency: transitive
    description:
      name: pay_android
      sha256: "561e579f23e67da5299be0758831db9355315d92ba7c3b1904a9b20f905183ca"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  pay_ios:
    dependency: transitive
    description:
      name: pay_ios
      sha256: d87ccd6a25ea843bd88f689155543067d83a82c5f6ff963a6876f402820bf6c1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.12"
  pay_platform_interface:
    dependency: transitive
    description:
      name: pay_platform_interface
      sha256: "39a9f5fa4bafc59ce103bd5e84a6362113c6e58c361aed8dde9494b182fac400"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  pdf:
    dependency: "direct main"
    description:
      name: pdf
      sha256: "05df53f8791587402493ac97b9869d3824eccbc77d97855f4545cf72df3cae07"
      url: "https://pub.dev"
    source: hosted
    version: "3.11.1"
  pdf_widget_wrapper:
    dependency: transitive
    description:
      name: pdf_widget_wrapper
      sha256: c930860d987213a3d58c7ec3b7ecf8085c3897f773e8dc23da9cae60a5d6d0f5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  pdfx:
    dependency: "direct main"
    description:
      name: pdfx
      sha256: cbbd7bf54d6f37524df85d06a816fa095d124cd32d42909effddc0027f9db10b
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  percent_indicator:
    dependency: "direct main"
    description:
      name: percent_indicator
      sha256: "0d77d5c6fa9b7f60202cedf748b568ba9ba38d3f30405d6ceae4da76f5185462"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  perfect_freehand:
    dependency: "direct main"
    description:
      name: perfect_freehand
      sha256: "6ced289209b3b26dc23c8d21960ceff3d39442cebd8e12ce722c4385c63553e5"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "59adad729136f01ea9e35a48f5d1395e25cba6cea552249ddbe9cf950f5d7849"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d3971dcdd76182a0c198c096b5db2f0884b0d4196723d21a866fc4cdea057ebc
      url: "https://pub.dev"
    source: hosted
    version: "12.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f84a188e79a35c687c132a0a0556c254747a08561e99ab933f12f6ca71ef3c98
      url: "https://pub.dev"
    source: hosted
    version: "9.4.6"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "38f000e83355abb3392140f6bc3030660cfaef189e1f87824facb76300b4ff24"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+5"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: eb99b295153abce5d683cac8c02e22faab63e50679b937fa1bf67d58bb282878
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  photo_view:
    dependency: "direct main"
    description:
      name: photo_view
      sha256: "1fc3d970a91295fbd1364296575f854c9863f225505c28c46e0a03e48960c75e"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.0"
  pinput:
    dependency: "direct main"
    description:
      name: pinput
      sha256: "7bf9aa7d0eeb3da9f7d49d2087c7bc7d36cd277d2e94cc31c6da52e1ebb048d0"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  platform_detect:
    dependency: transitive
    description:
      name: platform_detect
      sha256: a62f99417fc4fa2d099ce0ccdbb1bd3977920f2a64292c326271f049d4bc3a4f
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  platform_object_channel:
    dependency: transitive
    description:
      name: platform_object_channel
      sha256: "033f3ee122aabf21f9bd372d4440cc4ad00e94896f40f6e1bb1bc92b8676d702"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  platform_object_channel_android:
    dependency: transitive
    description:
      name: platform_object_channel_android
      sha256: "22b9df43c5e19a4950857cd42d69e27272ce70431aea2b959444496881957705"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  platform_object_channel_foundation:
    dependency: transitive
    description:
      name: platform_object_channel_foundation
      sha256: "317097cd7242d65a841b5295a52aa11dd238933f19576acc1f007cb136faff2e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  platform_object_channel_interface:
    dependency: transitive
    description:
      name: platform_object_channel_interface
      sha256: ba45377de29f059ee70ce6b0169dbcb1ef64d5219693d2780b7bec5c5750e649
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pluto_grid:
    dependency: "direct main"
    description:
      name: pluto_grid
      sha256: "1d4cd9d2652742b556aa9b3230cc64672a3f63c34a9acc80fef794ab36ad903b"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  pointer_interceptor:
    dependency: "direct main"
    description:
      name: pointer_interceptor
      sha256: "57210410680379aea8b1b7ed6ae0c3ad349bfd56fe845b8ea934a53344b9d523"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1+2"
  pointer_interceptor_ios:
    dependency: transitive
    description:
      name: pointer_interceptor_ios
      sha256: a6906772b3205b42c44614fcea28f818b1e5fdad73a4ca742a7bd49818d9c917
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  pointer_interceptor_platform_interface:
    dependency: transitive
    description:
      name: pointer_interceptor_platform_interface
      sha256: "0597b0560e14354baeb23f8375cd612e8bd4841bf8306ecb71fcd0bb78552506"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0+1"
  pointer_interceptor_web:
    dependency: transitive
    description:
      name: pointer_interceptor_web
      sha256: "7a7087782110f8c1827170660b09f8aa893e0e9a61431dbbe2ac3fc482e8c044"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.2+1"
  polylabel:
    dependency: transitive
    description:
      name: polylabel
      sha256: "41b9099afb2aa6c1730bdd8a0fab1400d287694ec7615dd8516935fa3144214b"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  postgrest:
    dependency: transitive
    description:
      name: postgrest
      sha256: "10b81a23b1c829ccadf68c626b4d66666453a1474d24c563f313f5ca7851d575"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  pretty_dio_logger:
    dependency: "direct main"
    description:
      name: pretty_dio_logger
      sha256: "36f2101299786d567869493e2f5731de61ce130faa14679473b26905a92b6407"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  printing:
    dependency: "direct main"
    description:
      name: printing
      sha256: b535d177fc6e8f8908e19b0ff5c1d4a87e3c4d0bf675e05aa2562af1b7853906
      url: "https://pub.dev"
    source: hosted
    version: "5.13.4"
  pro_image_editor:
    dependency: "direct main"
    description:
      name: pro_image_editor
      sha256: e9798eb7c965f1f9387c0397053c68b97c73dabd5a9ca58f148b046199aae86f
      url: "https://pub.dev"
    source: hosted
    version: "9.1.0"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "107d8be718f120bbba9dcd1e95e3bd325b1b4a4f07db64154635ba03f2567a0d"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  proj4dart:
    dependency: transitive
    description:
      name: proj4dart
      sha256: c8a659ac9b6864aa47c171e78d41bbe6f5e1d7bd790a5814249e6b68bc44324e
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  prompts:
    dependency: transitive
    description:
      name: prompts
      sha256: "3773b845e85a849f01e793c4fc18a45d52d7783b4cb6c0569fad19f9d0a774a1"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  protobuf:
    dependency: "direct main"
    description:
      name: protobuf
      sha256: "68645b24e0716782e58948f8467fd42a880f255096a821f9e7d0ec625b00c84d"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  protoc_plugin:
    dependency: "direct main"
    description:
      name: protoc_plugin
      sha256: fb0554851c9eca30bd18405fbbfe81e39166d4a2f0e5b770606fd69da3da0b2f
      url: "https://pub.dev"
    source: hosted
    version: "21.1.2"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "7b3cfbf654f3edd0c6298ecd5be782ce997ddf0e00531b9464b55245185bbbbd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  pub_updater:
    dependency: transitive
    description:
      name: pub_updater
      sha256: "54e8dc865349059ebe7f163d6acce7c89eb958b8047e6d6e80ce93b13d7c9e60"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  pubspec:
    dependency: transitive
    description:
      name: pubspec
      sha256: f534a50a2b4d48dc3bc0ec147c8bd7c304280fff23b153f3f11803c4d49d927e
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "81876843eb50dc2e1e5b151792c9a985c5ed2536914115ed04e9c8528f6647b0"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  python_ffi_cpython_dart:
    dependency: transitive
    description:
      name: python_ffi_cpython_dart
      sha256: "0a70042544ffb327113256445ba2b44c69e354eaa63c13e8270e099f2d60bbc1"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.4"
  python_ffi_dart:
    dependency: transitive
    description:
      name: python_ffi_dart
      sha256: "9c11a990d488822512b0324384fa06849aa356f63d13b4bc244178e3684d32cc"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.4"
  python_ffi_interface:
    dependency: transitive
    description:
      name: python_ffi_interface
      sha256: cb4b5c918b9b4f68f584bedb3c71c2b3ac8887239df13c360c973c9a2e13395a
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5a1d2586170e172b8a8c8470bbbffd5eb0cd38a66c0d77155ea138d3af3a4445"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  qs_dart:
    dependency: transitive
    description:
      name: qs_dart
      sha256: "56734fa99a8cc43d72b7396f26c0dad7d1a4ae4bfedc614e94e07ae7a833a179"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  quick_actions:
    dependency: "direct main"
    description:
      name: quick_actions
      sha256: "7e35dd6a21f5bbd21acf6899039eaf85001a5ac26d52cbd6a8a2814505b90798"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  quick_actions_android:
    dependency: transitive
    description:
      name: quick_actions_android
      sha256: "926e50d6f879287b34d21934e6c9457f0d851f554179f2a9e8136c4acd1b7062"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.18"
  quick_actions_ios:
    dependency: transitive
    description:
      name: quick_actions_ios
      sha256: "837b7e6b5973784d3da56b8c959b446b215914f20405d88cd7d22a2fb94e4e4c"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  quick_actions_platform_interface:
    dependency: transitive
    description:
      name: quick_actions_platform_interface
      sha256: "1fec7068db5122cd019e9340d3d7be5d36eab099695ef3402c7059ee058329a4"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  quick_quiz:
    dependency: "direct main"
    description:
      name: quick_quiz
      sha256: c4c9ae08fe9d1ac39697316ca8087cf7ba65ea48fac4cd0121b7e1ac0a6fdef1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  quick_quiz_view:
    dependency: "direct main"
    description:
      name: quick_quiz_view
      sha256: "93bb36ba9c78637bbd38a53956c4023ef24c03f116fc3d532d6d5f5fdc967896"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  quill_native_bridge:
    dependency: transitive
    description:
      name: quill_native_bridge
      sha256: "7e2050567c5dae3516b6c399fdd2036971aa16114e19c51832523f2ec1b8faca"
      url: "https://pub.dev"
    source: hosted
    version: "10.6.2"
  quiver:
    dependency: "direct main"
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  quiz_bank:
    dependency: "direct main"
    description:
      name: quiz_bank
      sha256: "4e43f92e1bc455f8210bd347a3ae4673185f3af10f669cd809c614faf15f7a8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  quote_buffer:
    dependency: transitive
    description:
      name: quote_buffer
      sha256: c4cd07e55ed1b1645a1cc74278a03b2a642c9f6ea3c0528d51827fdd320acf87
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6"
  random_avatar:
    dependency: "direct main"
    description:
      name: random_avatar
      sha256: "1468b060ac4324fa4f6aeeced732079638d9b6a64838b705ecbe9f208bd1609b"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.8"
  re_editor:
    dependency: "direct main"
    description:
      name: re_editor
      sha256: "2169c114c7877bcaae72d6e8b69cdaa2a9cded69a51e3cf26209dad4a3ed2b9c"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  re_highlight:
    dependency: transitive
    description:
      name: re_highlight
      sha256: "6c4ac3f76f939fb7ca9df013df98526634e17d8f7460e028bd23a035870024f2"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  reactive_forms:
    dependency: "direct main"
    description:
      name: reactive_forms
      sha256: "9b1fb18e0aae9c50cfa0aaabaaa38bc4d78eefc9b7b95fa9c947b051f6524b8e"
      url: "https://pub.dev"
    source: hosted
    version: "17.0.1"
  reactive_forms_annotations:
    dependency: "direct main"
    description:
      name: reactive_forms_annotations
      sha256: "1b65ebadd717aa4c88bc9431d8ae717f13a0c2ee5389698c132caa7a84489e4c"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  reactive_forms_generator:
    dependency: "direct dev"
    description:
      name: reactive_forms_generator
      sha256: "34206d3d1f57f2157b291d105a0a0a83e65116a0325706edddff7927ae7cc925"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  readmore:
    dependency: "direct main"
    description:
      name: readmore
      sha256: e8fca2bd397b86342483b409e2ec26f06560a5963aceaa39b27f30722b506187
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  realtime_client:
    dependency: transitive
    description:
      name: realtime_client
      sha256: "3a0a99b5bd0fc3b35e8ee846d9a22fa2c2117f7ef1cb73d1e5f08f6c3d09c4e9"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  recase:
    dependency: transitive
    description:
      name: recase
      sha256: e4eb4ec2dcdee52dcf99cb4ceabaffc631d7424ee55e56f280bc039737f89213
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  record:
    dependency: "direct main"
    description:
      name: record
      sha256: "8cb57763d954624fbc673874930c6f1ceca3baaf9bfee24b25da6fd451362394"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  record_android:
    dependency: transitive
    description:
      name: record_android
      sha256: "0b4739a2502fff402b0ac0ff1d6b2740854d116d78e06a4a16b3989821f84446"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  record_darwin:
    dependency: transitive
    description:
      name: record_darwin
      sha256: e487eccb19d82a9a39cd0126945cfc47b9986e0df211734e2788c95e3f63c82c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  record_linux:
    dependency: transitive
    description:
      name: record_linux
      sha256: "74d41a9ebb1eb498a38e9a813dd524e8f0b4fdd627270bda9756f437b110a3e3"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  record_platform_interface:
    dependency: transitive
    description:
      name: record_platform_interface
      sha256: "8a575828733d4c3cb5983c914696f40db8667eab3538d4c41c50cbb79e722ef4"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  record_web:
    dependency: transitive
    description:
      name: record_web
      sha256: "10cb041349024ce4256e11dd35874df26d8b45b800678f2f51fd1318901adc64"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  record_windows:
    dependency: transitive
    description:
      name: record_windows
      sha256: "7bce0ac47454212ca8bfa72791d8b6a951f2fb0d4b953b64443c014227f035b4"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  recursive_regex:
    dependency: transitive
    description:
      name: recursive_regex
      sha256: f7252e3d3dfd1665e594d9fe035eca6bc54139b1f2fee38256fa427ea41adc60
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  redux:
    dependency: transitive
    description:
      name: redux
      sha256: "1e86ed5b1a9a717922d0a0ca41f9bf49c1a587d50050e9426fc65b14e85ec4d7"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  regex_range:
    dependency: transitive
    description:
      name: regex_range
      sha256: a3bdc3bb252932272ec05d16b87242770fabaac6ad32d48c216980956d9743fe
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0+1"
  responsive_framework:
    dependency: "direct main"
    description:
      name: responsive_framework
      sha256: a8e1c13d4ba980c60cbf6fa1e9907cd60662bf2585184d7c96ca46c43de91552
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  retrofit:
    dependency: "direct main"
    description:
      name: retrofit
      sha256: "3c9885ef3dbc5dc4b3fb0a40c972ab52e4dad04d52dac9bba24dfa76cf100451"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.1"
  retrofit_generator:
    dependency: "direct dev"
    description:
      name: retrofit_generator
      sha256: f76fdb2b66854690d5a332e7364d7561fc9dc2b3c924d7956ab8070495e21f6a
      url: "https://pub.dev"
    source: hosted
    version: "9.1.5"
  retry:
    dependency: "direct main"
    description:
      name: retry
      sha256: "822e118d5b3aafed083109c72d5f484c6dc66707885e07c0fbcb8b986bba7efc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  riverpod:
    dependency: "direct main"
    description:
      name: riverpod
      sha256: "59062512288d3056b2321804332a13ffdd1bf16df70dcc8e506e411280a72959"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  rnd:
    dependency: "direct main"
    description:
      name: rnd
      sha256: "87799a8c447da2e728096264b2cc13679a11af49f0504331f4d588986a371fcf"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  rxdart:
    dependency: "direct main"
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "12669c4a913688a26555323fb9cec373d8f9fbe091f2d01c40c723b33caa8989"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      sha256: "63817697a7835e6ce82add4228e15d233b74d42975c143ad8cfe07009fab866b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  scroll_pos:
    dependency: transitive
    description:
      name: scroll_pos
      sha256: cebf602b2dd939de6832bb902ffefb574608d1b84f420b82b381a4007d3c1e1b
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  scroll_to_index:
    dependency: transitive
    description:
      name: scroll_to_index
      sha256: b707546e7500d9f070d63e5acf74fd437ec7eeeb68d3412ef7b0afada0b4f176
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  scrollable_positioned_list:
    dependency: transitive
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  sdp_transform:
    dependency: transitive
    description:
      name: sdp_transform
      sha256: "73e412a5279a5c2de74001535208e20fff88f225c9a4571af0f7146202755e45"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2"
  sembast:
    dependency: "direct main"
    description:
      name: sembast
      sha256: "7119cf6f79bd1d48c8ec7943f4facd96c15ab26823021ed0792a487c7cd34441"
      url: "https://pub.dev"
    source: hosted
    version: "3.8.5+1"
  sensors_plus:
    dependency: "direct main"
    description:
      name: sensors_plus
      sha256: "905282c917c6bb731c242f928665c2ea15445aa491249dea9d98d7c79dc8fd39"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  sensors_plus_platform_interface:
    dependency: transitive
    description:
      name: sensors_plus_platform_interface
      sha256: "58815d2f5e46c0c41c40fb39375d3f127306f7742efe3b891c0b1c87e2b5cd5d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: fce43200aa03ea87b91ce4c3ac79f0cecd52e2a7a56c7a4185023c271fbfa6da
      url: "https://pub.dev"
    source: hosted
    version: "10.1.4"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: cc012a23fc2d479854e6c80150696c4a5f5bb62cb89af4de1c505cf78d0a5d0b
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "846849e3e9b68f3ef4b60c60cf4b3e02e9321bc7f4d8c4692cf87ffa82fc8a3a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "02a7d8a9ef346c9af715811b01fbd8e27845ad2c41148eefd31321471b41863d"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d2ca4132d3946fec2184261726b355836a82c33d7d5b67af32692aff18a4684e
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: e7dd780a7ffb623c57850b33f43309312fc863fb6aa3d276a754bb299839ef12
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: "89f967eca29607c933ba9571d838be31d67f53f6e4ee15147d5dc2934fee1b1e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: c87c3875f91262785dade62d135760c2c69cb217ac759485334c5857ad89f6e3
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: cc36c297b52866d203dbf9332263c94becc2fe0ceaa9681d07b6ef9807023b67
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  showcaseview:
    dependency: "direct main"
    description:
      name: showcaseview
      sha256: f236c1f44b286e1ba888f8701adca067af92c33e29ea937d0fe9b4a29d4cd41e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sign_in_with_apple:
    dependency: "direct main"
    description:
      name: sign_in_with_apple
      sha256: e84a62e17b7e463abf0a64ce826c2cd1f0b72dff07b7b275e32d5302d76fb4c5
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  sign_in_with_apple_platform_interface:
    dependency: transitive
    description:
      name: sign_in_with_apple_platform_interface
      sha256: c2ef2ce6273fce0c61acd7e9ff5be7181e33d7aa2b66508b39418b786cca2119
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  sign_in_with_apple_web:
    dependency: transitive
    description:
      name: sign_in_with_apple_web
      sha256: "2f7c38368f49e3f2043bca4b46a4a61aaae568c140a79aa0675dc59ad0ca49bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  signature:
    dependency: "direct main"
    description:
      name: signature
      sha256: "8056e091ad59c2eb5735fee975ec649d0caf8ce802bb1ffb1e0955b00a6d0daa"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.0"
  simple_gesture_detector:
    dependency: transitive
    description:
      name: simple_gesture_detector
      sha256: ba2cd5af24ff20a0b8d609cec3f40e5b0744d2a71804a2616ae086b9c19d19a3
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  sized_context:
    dependency: "direct main"
    description:
      name: sized_context
      sha256: "9921e6c09e018132c3e1c6a18e14febbc1cc5c87a200d64ff7578cb49991f6e7"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  sliding_up_panel:
    dependency: "direct main"
    description:
      name: sliding_up_panel
      sha256: "578e90956a6212d1e406373250b2436a0f3afece29aee3c24c8360094d6cf968"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0+1"
  sliver_tools:
    dependency: "direct main"
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.dev"
    source: hosted
    version: "0.2.12"
  smooth_page_indicator:
    dependency: "direct main"
    description:
      name: smooth_page_indicator
      sha256: "3b28b0c545fa67ed9e5997d9f9720d486f54c0c607e056a1094544e36934dff3"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0+3"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "14658ba5f669685cd3d63701d01b31ea748310f7ab854e471962670abcf57832"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "86d247119aedce8e63f4751bd9626fc9613255935558447569ad42f9f5b48b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.5"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: c0713a43e323c3302c2abe2a1cc89aa057a387101ebd280371d6a6c9fa68516b
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "190222579a448b03896e0ca6eca5998fa810fda630c1d65e2f78b3f638f54812"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.13"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  speech_to_text:
    dependency: "direct main"
    description:
      name: speech_to_text
      sha256: "6cf8f284997490ebef1d68f8707bef57dcf083f43c0f915cc285428520bfe6be"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  speech_to_text_platform_interface:
    dependency: transitive
    description:
      name: speech_to_text_platform_interface
      sha256: a1935847704e41ee468aad83181ddd2423d0833abe55d769c59afca07adb5114
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: "direct main"
    description:
      name: sqflite
      sha256: "2d7299468485dca85efeeadf5d38986909c5eb0cd71fd3db2c2f000e6c9454bb"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "78f489aab276260cdd26676d2169446c7ecd3484bbd5fead4ca14f3ed4dd9ee3"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "761b9740ecbd4d3e66b8916d784e581861fd3c3553eda85e167bc49fdb68f709"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4+6"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "96a698e2bc82bd770a4d6aab00b42396a7c63d9e33513a56945cbccb594c2474"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqlite3:
    dependency: "direct main"
    description:
      name: sqlite3
      sha256: cb7f4e9dc1b52b1fa350f7b3d41c662e75fc3d399555fa4e5efcf267e9a4fbb5
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  sqlite3_flutter_libs:
    dependency: "direct main"
    description:
      name: sqlite3_flutter_libs
      sha256: "73016db8419f019e807b7a5e5fbf2a7bd45c165fed403b8e7681230f3a102785"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.28"
  sse:
    dependency: "direct main"
    description:
      name: sse
      sha256: "4389a01d5bc7ef3e90fbc645f8e7c6d8711268adb1f511e14ae9c71de47ee32b"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.7"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  state_notifier:
    dependency: transitive
    description:
      name: state_notifier
      sha256: b8677376aa54f2d7c58280d5a007f9e8774f1968d1fb1c096adcb4792fba29bb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  step_progress_indicator:
    dependency: "direct main"
    description:
      name: step_progress_indicator
      sha256: b51bb1fcfc78454359f0658c5a2c21548c3825ebf76e826308e9ca10f383bbb8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  sticky_headers:
    dependency: "direct main"
    description:
      name: sticky_headers
      sha256: "9b3dd2cb0fd6a7038170af3261f855660cbb241cb56c501452cb8deed7023ede"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0+2"
  stomp_dart_client:
    dependency: "direct main"
    description:
      name: stomp_dart_client
      sha256: "376d37a01fbe865eb213ef857f54e1d8adcba203e5cfa54e1463cfcb9ebac84c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  storage_client:
    dependency: transitive
    description:
      name: storage_client
      sha256: "09bac4d75eea58e8113ca928e6655a09cc8059e6d1b472ee801f01fde815bcfc"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  string_validator:
    dependency: transitive
    description:
      name: string_validator
      sha256: a278d038104aa2df15d0e09c47cb39a49f907260732067d0034dc2f2e4e2ac94
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  stripe_android:
    dependency: transitive
    description:
      name: stripe_android
      sha256: bc919b07ba1560b25a69002f9ca67a5973f12bfb96f78033cf9bd6c561f1aa10
      url: "https://pub.dev"
    source: hosted
    version: "11.3.0"
  stripe_ios:
    dependency: transitive
    description:
      name: stripe_ios
      sha256: "734a7668babd85a451c8049f0dfa8ca5dc6d80f85600d29732e7fa703dd5ec5e"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.0"
  stripe_js:
    dependency: transitive
    description:
      name: stripe_js
      sha256: d12c628c82e0cea0f5c93c7751c8b01d5ef25f417797c270cd62713564cdee6f
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  stripe_platform_interface:
    dependency: transitive
    description:
      name: stripe_platform_interface
      sha256: "5cb987074b6ee199e7dd1b81d189c87583f6eaeb19147ffc8c15bc009596e6ce"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.0"
  supabase:
    dependency: transitive
    description:
      name: supabase
      sha256: f00172f5f0b2148ea1c573f52862d50cacb6f353f579f741fa35e51704845958
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  supabase_flutter:
    dependency: "direct main"
    description:
      name: supabase_flutter
      sha256: d88eccf9e46e57129725a08e72a3109b6f780921fdc27fe3d7669a11ae80906b
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  super_editor:
    dependency: "direct main"
    description:
      name: super_editor
      sha256: "5432698b469eb36f67ec6bff4b9aada0edda730d2a509d5d5c560e0ba6a3bf06"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0-dev.12"
  super_keyboard:
    dependency: transitive
    description:
      name: super_keyboard
      sha256: c8e303cd7bc1fc62732213f0f2660273a078be23eae7a4219d0ab3dd0b0ccb9a
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  super_text_layout:
    dependency: transitive
    description:
      name: super_text_layout
      sha256: "0c85c295164edaab175c477584f2b7abc295dfd2a7f81987a88e538cf75b7898"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.17"
  swagger_dart_code_generator:
    dependency: "direct dev"
    description:
      name: swagger_dart_code_generator
      sha256: e6fab279c2adb3f91aa170c9126601d22e1485217dddc1443cf3c05eb6480d45
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  sync_http:
    dependency: transitive
    description:
      name: sync_http
      sha256: "7f0cd72eca000d2e026bcd6f990b81d0ca06022ef4e32fb257b30d3d1014a961"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "69fe30f3a8b04a0be0c15ae6490fc859a78ef4c43ae2dd5e8a623d45bfcf9225"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0+3"
  system_info2:
    dependency: "direct main"
    description:
      name: system_info2
      sha256: "65206bbef475217008b5827374767550a5420ce70a04d2d7e94d1d2253f3efc9"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  table_calendar:
    dependency: "direct main"
    description:
      name: table_calendar
      sha256: b2896b7c86adf3a4d9c911d860120fe3dbe03c85db43b22fd61f14ee78cdbb63
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test:
    dependency: "direct dev"
    description:
      name: test
      sha256: "301b213cd241ca982e9ba50266bd3f5bd1ea33f1455554c5abb85d1be0e2d87e"
      url: "https://pub.dev"
    source: hosted
    version: "1.25.15"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  test_core:
    dependency: "direct main"
    description:
      name: test_core
      sha256: "84d17c3486c8dfdbe5e12a50c8ae176d15e2a771b96909a9442b40173649ccaa"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.8"
  time:
    dependency: transitive
    description:
      name: time
      sha256: "370572cf5d1e58adcb3e354c47515da3f7469dac3a95b447117e728e7be6f461"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  timeago:
    dependency: "direct main"
    description:
      name: timeago
      sha256: "054cedf68706bb142839ba0ae6b135f6b68039f0b8301cbe8784ae653d5ff8de"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.0"
  timeline_tile:
    dependency: "direct main"
    description:
      name: timeline_tile
      sha256: "85ec2023c67137397c2812e3e848b2fb20b410b67cd9aff304bb5480c376fc0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: ffc9d5f4d1193534ef051f9254063fa53d588609418c84299956c3db9383587d
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "62ee18aca144e4a9f29d212f5a4c6a053be252b895ab14b5821996cff4ed90fe"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  token_parser:
    dependency: "direct main"
    description:
      name: token_parser
      sha256: f486437b197f4dae459defa03b4320fd042be08e9959fa80f32af99696f03842
      url: "https://pub.dev"
    source: hosted
    version: "1.7.0"
  touchable:
    dependency: transitive
    description:
      name: touchable
      sha256: "50040b39778bb1adc8a480625de8681253bd2134785395cf16abd66bb13d3ed0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  transparent_image:
    dependency: transitive
    description:
      name: transparent_image
      sha256: e8991d955a2094e197ca24c645efec2faf4285772a4746126ca12875e54ca02f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  tuple:
    dependency: transitive
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  twitter_login:
    dependency: transitive
    description:
      name: twitter_login
      sha256: "31ff9db2e37eda878b876a4ce6d1525f51d34b6cd9de9aa185b07027a23ab95b"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  unicode:
    dependency: transitive
    description:
      name: unicode
      sha256: "0f69e46593d65245774d4f17125c6084d2c20b4e473a983f6e21b7d7762218f1"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  universal_html:
    dependency: "direct main"
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: "direct main"
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  unsplash_client:
    dependency: "direct main"
    description:
      name: unsplash_client
      sha256: "9827f4c1036b7a6ac8cb3f404ac179df7441eee69371d9b17f181817fe502fd7"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  upgrader:
    dependency: "direct main"
    description:
      name: upgrader
      sha256: eb5a4888873d7998605306c4212491efdff8e59ee609d946dbcd6df83598d004
      url: "https://pub.dev"
    source: hosted
    version: "11.3.1"
  uri:
    dependency: "direct main"
    description:
      name: uri
      sha256: "889eea21e953187c6099802b7b4cf5219ba8f3518f604a1033064d45b1b8268a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "6fc2f56536ee873eeb867ad176ae15f304ccccc357848b351f6f0d8d4a40d193"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.14"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "16a513b6c12bb419304e72ea0ae2ab4fed569920d1c7cb850263fe3acc824626"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "44cf3aabcedde30f2dba119a9dea3b0f2672fbe6fa96e85536251d678216b3c4"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  url_strategy:
    dependency: "direct main"
    description:
      name: url_strategy
      sha256: "6eff69fa0900b731a23552b38b54389f399d247dbb0998f2cbdf25bef6790a7c"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "27d5fefe86fb9aace4a9f8375b56b3c292b64d8c04510df230f849850d912cb7"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.15"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "2430b973a4ca3c4dbc9999b62b8c719a160100dcbae5c819bae0cacce32c9cdb"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.12"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "1b4b9e706a10294258727674a340ae0d6e64a7231980f9f9a3d12e4b42407aad"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.16"
  vector_math:
    dependency: "direct main"
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  version:
    dependency: transitive
    description:
      name: version
      sha256: "3d4140128e6ea10d83da32fef2fa4003fccbf6852217bb854845802f04191f94"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  vertex_ai:
    dependency: transitive
    description:
      name: vertex_ai
      sha256: "8bdbc23b8cccd80d17d79a190cd4a060f844668c72969c2ff1ff1413943e9de1"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0+3"
  vertical_card_pager:
    dependency: "direct main"
    description:
      name: vertical_card_pager
      sha256: "6ab7ea3f227bf61e67359e85785aff6a64df605f0568873fc8d1606d92a2449c"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.2"
  video_editor:
    dependency: "direct main"
    description:
      name: video_editor
      sha256: "263be52e052118389f372f055f59c2fda5c7beecfdb706b899d2e05be8740c22"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "4a8c3492d734f7c39c2588a3206707a05ee80cef52e8c7f3b2078d430c84bc17"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.2"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "391e092ba4abe2f93b3e625bd6b6a6ec7d7414279462c1c0ee42b5ab8d0a0898"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.16"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "33224c19775fd244be2d6e3dbd8e1826ab162877bd61123bf71890772119a2b7"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.5"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "229d7642ccd9f3dc4aba169609dd6b5f3f443bb4cc15b82f7785fcada5af9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.3"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "881b375a934d8ebf868c7fb1423b2bfaa393a0a265fa3f733079a86536064a10"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  video_thumbnail:
    dependency: transitive
    description:
      name: video_thumbnail
      sha256: "3455c189d3f0bb4e3fc2236475aa84fe598b9b2d0e08f43b9761f5bc44210016"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.3"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  wakelock_plus:
    dependency: transitive
    description:
      name: wakelock_plus
      sha256: "36c88af0b930121941345306d259ec4cc4ecca3b151c02e3a9e71aede83c615e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.10"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "70e780bc99796e1db82fe764b1e7dcb89a86f1e5b3afb1db354de50f2e41eb7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  watch_it:
    dependency: "direct main"
    description:
      name: watch_it
      sha256: a01a9e8292c040de82670f28f8a7d35315115a22f3674d2c4a8fd811fd1ac0ab
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "69da27e49efa56a15f8afe8f4438c4ec02eff0a117df1b22ea4aad194fe1c104"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  waveform_flutter:
    dependency: transitive
    description:
      name: waveform_flutter
      sha256: "08c9e98d4cf119428d8b3c083ed42c11c468623eaffdf30420ae38e36662922a"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  waveform_recorder:
    dependency: transitive
    description:
      name: waveform_recorder
      sha256: "139ce4442bd460441fcd94d9a242cdfa07ff08cd21d82a4f4806a44c17ddf9ab"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.3"
  weak_map:
    dependency: transitive
    description:
      name: weak_map
      sha256: bf2cd4bbdba35cf407c41a290085240715ecfba82ea553e78fdeab6fe56d915e
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web_socket:
    dependency: transitive
    description:
      name: web_socket
      sha256: "3c12d96c0c9a4eec095246debcea7b86c0324f22df69893d538fcc6f1b8cce83"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  web_socket_channel:
    dependency: "direct main"
    description:
      name: web_socket_channel
      sha256: "9f187088ed104edd8662ca07af4b124465893caf063ba29758f97af57e61da8f"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  web_socket_client:
    dependency: "direct main"
    description:
      name: web_socket_client
      sha256: "0ec5230852349191188c013112e4d2be03e3fc83dbe80139ead9bf3a136e53b5"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  webdriver:
    dependency: transitive
    description:
      name: webdriver
      sha256: "2f3a14ca026957870cfd9c635b83507e0e51d8091568e90129fbf805aba7cade"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "87d3f2333bb240704cd3f1c6b5b7acd8a10e7f0bc28c28dcf14e782014f4a572"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  webrtc_interface:
    dependency: transitive
    description:
      name: webrtc_interface
      sha256: e05f00091c9c70a15bab4ccb1b6c46d9a16a6075002f02cfac3641eccb05e25d
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1+hotfix.1"
  websocket_universal:
    dependency: "direct main"
    description:
      name: websocket_universal
      sha256: "8e50d8238a26147fed64512e7af200ef9ab73fab51ab56010f7a54f94a0bb480"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.5"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "3d535126f7244871542b2f0b0fcf94629c9a14883250461f9abe1a6644c1c379"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: b7e92f129482460951d96ef9a46b49db34bd2e1621685de26e9eaafd9674e7eb
      url: "https://pub.dev"
    source: hosted
    version: "3.16.3"
  whisper_library_flutter:
    dependency: "direct main"
    description:
      name: whisper_library_flutter
      sha256: "1182049f7f0eaf18248e789facf2e12d7c754c2b988e7146e2c1947cd7dc458e"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.9"
  win32:
    dependency: "direct main"
    description:
      name: win32
      sha256: "8b338d4486ab3fbc0ba0db9f9b4f5239b6697fcee427939a40e720cbb9ee0a69"
      url: "https://pub.dev"
    source: hosted
    version: "5.9.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  wkt_parser:
    dependency: transitive
    description:
      name: wkt_parser
      sha256: "8a555fc60de3116c00aad67891bcab20f81a958e4219cc106e3c037aa3937f13"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: "direct main"
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  xml2json:
    dependency: "direct main"
    description:
      name: xml2json
      sha256: "9523203b99032ce419672804010cce72ea47fc277b3135f77bbb7ac8fa391664"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.5"
  xrange:
    dependency: transitive
    description:
      name: xrange
      sha256: "17bb75bd5eeae7152680f9b748880bf88a670b61f360266df56f7bd239cb6343"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  yaml:
    dependency: "direct main"
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  yaml_edit:
    dependency: transitive
    description:
      name: yaml_edit
      sha256: fb38626579fb345ad00e674e2af3a5c9b0cc4b9bfb8fd7f7ff322c7c9e62aef5
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  yet_another_json_isolate:
    dependency: transitive
    description:
      name: yet_another_json_isolate
      sha256: fe45897501fa156ccefbfb9359c9462ce5dec092f05e8a56109db30be864f01e
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  youtube_player_iframe:
    dependency: "direct main"
    description:
      name: youtube_player_iframe
      sha256: "66020f7756accfb22b3297565d845f9bef14249c730dd51e1ec648fa155fb24a"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  youtube_player_iframe_web:
    dependency: transitive
    description:
      name: youtube_player_iframe_web
      sha256: "05222a228937932e7ee7a6171e8020fee4cd23d1c7bf6b4128c569484338c593"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
sdks:
  dart: "3.8.1"
  flutter: ">=3.29.0"
